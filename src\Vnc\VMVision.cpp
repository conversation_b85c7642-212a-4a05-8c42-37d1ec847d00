// VMVision.cpp

#include "VMVision.h"
#include <iostream>
#include <opencv2/imgcodecs.hpp> // 用于 cv::imread
#include "ScreenFrameDistributor.h"
#include "Core/ConfigManager.h" // 引入配置管理器
#include <queue> 
#include <mutex>
#include <condition_variable>
#include <chrono>
#include <string>
#include <future>
#include <atomic>
#include <thread>
#include <memory>
#include <GUI/consolelog/consolelog.h>
#include "SimpleOCR.h"  // PaddleOCR头文件
#include <vector>
#include <opencv2/opencv.hpp>
#include "Core/ThreadPool.h" // 线程池实现
#include <cfloat> // For DBL_MAX
#include <config/TemplatePathManager.h>
#include "VMTasks.h" // 添加VMTasks.h头文件
#include "VMStateManager.h" // 添加状态管理器头文件
#include <filesystem>
#include <windows.h> // For GetCurrentDirectoryA

// 前向声明
std::tuple<cv::Point, float> findTextLocation(PaddleOCR::SimpleOCR& ocr, const cv::Mat& image, const std::string& text, const std::string& vmName);
cv::Point mapToOriginal(const cv::Point& slicePoint, int sliceNumber, int imageWidth, int imageHeight, 
                       int horizontal_slices, int vertical_slices, float overlap_ratio);

// Slicing constants
const int SLICING_THRESHOLD = 1000; // Pixels
const int DEFAULT_SLICE_WIDTH = 800; // Default width for a slice
const int SLICE_OVERLAP = 100;      // Overlap between slices
// 切片分发器已在头文件中声明

// 定义切片结构体
struct ImageSlice {
    cv::Mat image;         // 切片图像
    cv::Rect region;       // 切片在原图中的区域
    std::string text;      // 识别出的文本
    cv::Point center;      // 文本中心点在切片中的坐标
    float confidence;      // 识别置信度
};

// 初始化静态标志，默认允许处理帧更新
std::atomic<bool> VMVision::s_allowFrameUpdates(true);

// 宏定义兼容不同版本的libvnc
#ifdef _LIBVNCCLIENT_FRAMEBUFFER_
#define FRAMEBUFFER _LIBVNCCLIENT_FRAMEBUFFER_
#else
#define FRAMEBUFFER frameBuffer
#endif

VMVision::VMVision(const std::string& vmName, std::shared_ptr<rfbClient> client)
    : vmName_(vmName), client_(client), running_(false), newFrameAvailable_(false),
    ocrThreadPoolSize_(2), matchThreadPoolSize_(2),
    ocrInitialized_(false), ocrEngine_(nullptr),
    lastFrameLogTime_(std::chrono::steady_clock::now() - std::chrono::seconds(11)) // Initialize last log time
{
    // 初始化屏幕切片器，默认1x2网格
    screenDistributor_ = std::make_unique<ScreenFrameDistributor>(1, 2);
    
    // 创建线程池
    try {
        ocrThreadPool_ = std::make_unique<ThreadPool>(ocrThreadPoolSize_);
        visionThreadPool_ = std::make_unique<ThreadPool>(matchThreadPoolSize_);
        AddLogInfo(LogLevel::Info, "[VMVision] " + vmName_ + " 线程池创建成功 (OCR:" + std::to_string(ocrThreadPoolSize_) + ", Vision:" + std::to_string(matchThreadPoolSize_) + ")");
    } catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "[VMVision] " + vmName_ + " 线程池创建失败: " + std::string(e.what()));
    }
    
    // 重新启用帧更新处理
    s_allowFrameUpdates.store(true);
    // 检查客户端指针是否有效
    auto currentClient = client_.lock();
    if (!currentClient) {
        AddLogInfo(LogLevel::Error, "VNC指针不存在");
        return;
    }

    // 设置帧缓冲区更新回调函数
    if (currentClient) {
        // 确保frameBuffer存在，防止后续访问出现段错误
        if (!currentClient->frameBuffer) {
            AddLogInfo(LogLevel::Warning, "VNC客户端帧缓冲区不存在");
            // 不要在这里设置回调，避免在无效的帧缓冲区上操作
            return;
        }
        currentClient->GotFrameBufferUpdate = onFrameBufferUpdate;

        // 将当前 VMVision 实例的指针存储在 clientData 中，以便在回调中使用
        currentClient->clientData = reinterpret_cast<rfbClientData*>(this);
    }
    else {
        AddLogInfo(LogLevel::Error, "客户端指针已失效，无法设置回调函数");
    }

    // 延迟初始化 OCR 引擎，在需要时才初始化
    // 这样可以避免在构造函数中可能出现的竞争条件
    ocrEngine_ = nullptr;
    ocrInitialized_ = false;

}

// Helper function to generate slices for a given region
std::vector<cv::Rect> generateSlices(const cv::Rect& region, int sliceWidth, int overlap) {
    std::vector<cv::Rect> slices;
    if (region.width <= sliceWidth) { // No slicing needed if region is smaller than or equal to sliceWidth
        slices.push_back(cv::Rect(0, 0, region.width, region.height)); // Use the original region (relative coords)
        return slices;
    }

    int currentX = 0;
    while (currentX < region.width) {
        int currentSliceActualWidth = std::min(sliceWidth, region.width - currentX);
        slices.push_back(cv::Rect(currentX, 0, currentSliceActualWidth, region.height));

        if (currentX + currentSliceActualWidth >= region.width) { // Last slice
            break;
        }
        currentX += (sliceWidth - overlap);
        if (currentX >= region.width) { // Should not happen if logic is correct, but as a safeguard
            break;
        }
    }
    // Ensure the last slice reaches the end of the region if overlap logic caused it to fall short
    if (!slices.empty()) {
        cv::Rect& lastSlice = slices.back();
        if (lastSlice.x + lastSlice.width < region.width) {
            lastSlice.width = region.width - lastSlice.x;
        }
    }
    else if (region.width > 0 && region.height > 0) { // Should have at least one slice if region is valid
        slices.push_back(cv::Rect(0, 0, region.width, region.height));
    }

    return slices;
}

VMVision::~VMVision() {
    try {
        // 停止帧处理
        stopProcessing();

        // 禁止帧更新回调
        s_allowFrameUpdates.store(false);

        // 取消注册帧更新回调
        auto currentClient = client_.lock();
        if (currentClient) {
            // 首先清除客户端数据，防止回调访问已释放的对象
            currentClient->clientData = nullptr;

            // 然后取消回调
            currentClient->GotFrameBufferUpdate = nullptr;

            // 清除引用
            client_.reset();
        }

        // 清理资源
        {
            std::lock_guard<std::mutex> lock(ocrMutex_);

            // 释放自己管理的线程池
            if (ocrThreadPool_) {
                ocrThreadPool_.reset();
            }
            if (visionThreadPool_) {
                visionThreadPool_.reset();
            }

            // 释放OCR引擎资源 - 智能指针自动管理
            if (ocrEngine_) {
                ocrEngine_.reset(); // 智能指针自动释放，无需手动delete
            }

            // 清空模板集合
            {
                std::lock_guard<std::mutex> templatesLock(templatesMutex_);
                templates_.clear();
            }

            // 🔧 匹配请求队列已移除 - 简化调用链
        }

        // 删除：视觉处理器资源已清理完毕日志
    }
    catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "[VMVision] 清理资源时发生异常: " + std::string(e.what()));
    }
    catch (...) {
        AddLogInfo(LogLevel::Error, "[VMVision] 清理资源时发生未知异常");
    }
}


// 停止帧更新处理
void VMVision::stopProcessing() {
    try {


        // 设置停止请求标志
        stopRequested_.store(true);

        // 设置全局标志，禁止所有帧更新回调
        s_allowFrameUpdates.store(false);

        // 使用原子操作安全地取消注册帧更新回调
        auto currentClient = client_.lock(); // 原子读取
        if (currentClient) {
            // 首先清除客户端数据，防止回调访问已释放的对象
            currentClient->clientData = nullptr;

            // 然后取消回调
            currentClient->GotFrameBufferUpdate = nullptr;

            // 等待一小段时间，确保所有正在进行的回调都已完成
            std::this_thread::sleep_for(std::chrono::milliseconds(100));

            // 清除引用
            client_.reset(); // 原子写入
        }

        // 通知所有等待的条件变量，确保线程不会阻塞
        {
            // 使用写锁来更新帧状态
            std::unique_lock<std::shared_mutex> lock(frameMutex_);
            newFrameAvailable_ = true;
        }
        frameCV_.notify_all(); // 唤醒所有等待帧更新的线程

        // 🔧 匹配请求队列已移除 - 简化调用链

        // 删除：帧处理已完全停止日志
    }
    catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "[VMVision] 停止帧处理时发生异常: " + std::string(e.what()));
    }
    catch (...) {
        AddLogInfo(LogLevel::Error, "[VMVision] 停止帧处理时发生未知异常");
    }
}



// 新增: 异步模板匹配接口实现 - 简化版
void VMVision::matchTemplateAsync(const std::string& templateName,double threshold, cv::Rect roi,int sliceNumber, std::shared_ptr<std::promise<std::pair<bool, cv::Point>>> resultPromise) {
    //首先检查是否已请求停止
    if (stopRequested_.load()) {
        resultPromise->set_value({ false, cv::Point(-1, -1) });
        return;
    }

    // 🔧 简化：直接在这里处理，不再创建MatchRequest对象
    if (visionThreadPool_) {
        visionThreadPool_->enqueue([this, templateName, threshold, roi, sliceNumber, resultPromise]() {
            processTemplateMatchDirect(templateName, threshold, roi, sliceNumber, resultPromise);
        });
    }
    else {
        processTemplateMatchDirect(templateName, threshold, roi, sliceNumber, resultPromise);
    }
}

//视觉文字匹配2 异步文本匹配接口实现 - 简化版
void VMVision::findTextAsync(
    const std::string& wordsToMatch, 
    double threshold,
    cv::Rect roi,
    int sliceNumber,
    std::shared_ptr<std::promise<std::pair<bool, cv::Point>>> resultPromise) 
{
    if (stopRequested_.load()) {
        resultPromise->set_value({ false, cv::Point(-1, -1) });
        return;
    }

    // 🔧 简化：直接在这里处理，不再创建MatchRequest对象
    if (ocrThreadPool_) {
        ocrThreadPool_->enqueue([this, wordsToMatch, threshold, roi, sliceNumber, resultPromise]() {
            processTextMatchDirect(wordsToMatch, threshold, roi, sliceNumber, resultPromise);
        });
    }
    else {
        processTextMatchDirect(wordsToMatch, threshold, roi, sliceNumber, resultPromise);
    }
}

// 异步文本识别接口
void VMVision::recognizeTextAsync(
    cv::Rect roi,
    int sliceNumber,
    std::shared_ptr<std::promise<TextRecognizeResult>> resultPromise) 
{
    if (stopRequested_.load()) {
        TextRecognizeResult res;
        res.success = false;
        res.errorMessage = "处理已停止";
        resultPromise->set_value(res);
        return;
    }
    
    // 直接调用processTextRecognizeDirect，不通过processMatchRequest
    if (ocrThreadPool_) {
        ocrThreadPool_->enqueue([this, roi, sliceNumber, resultPromise]() {
            processTextRecognizeDirect(roi, sliceNumber, resultPromise);
        });
    }
    else {

        processTextRecognizeDirect(roi, sliceNumber, resultPromise);
    }
}


// onFrameBufferUpdate 回调函数 (静态函数)
void VMVision::onFrameBufferUpdate(rfbClient* client, int x, int y, int w, int h) {

    // 首先检查全局标志，如果不允许处理帧更新，直接返回
    if (!s_allowFrameUpdates.load()) {
        return;
    }

    // 安全检查：客户端指针是否有效
    if (!client) {
        AddLogInfo(LogLevel::Debug, "[VMVision] 回调中收到空客户端指针");
        return;
    }

    // 安全检查：clientData是否有效
    if (!client->clientData) {
        AddLogInfo(LogLevel::Debug, "[VMVision] 客户端数据为空");
        return;
    }

    // 安全获取 VMVision 实例
    VMVision* vision = nullptr;
    try {
        vision = reinterpret_cast<VMVision*>(client->clientData);
        if (!vision) {
            AddLogInfo(LogLevel::Debug, "[VMVision] 无法获取视觉处理器实例");
            return;
        }
    }
    catch (...) {
        AddLogInfo(LogLevel::Warning, "[VMVision] 获取视觉处理器实例时发生异常");
        return;
    }

    // 检查是否请求停止处理
    if (vision->stopRequested_.load()) {
        return;
    }

    // 检查当前客户端是否与视觉处理器中的客户端相同
    auto currentClient = vision->client_.lock();
    if (!currentClient) {
        AddLogInfo(LogLevel::Debug, "[VMVision] 客户端指针不匹配");
        return;
    }

    try {
        // 安全检查帧缓冲区
        if (!client->frameBuffer) {
            AddLogInfo(LogLevel::Debug, "[VMVision] 帧缓冲区为空");
            return;
        }

        // 将 VNC 帧缓冲转换为 OpenCV Mat
        cv::Mat newFrame;
        try {
            newFrame = vision->rfbToCvMat(client);

        }
        catch (const std::exception& e) {
            AddLogInfo(LogLevel::Warning, "[VMVision] 转换帧缓冲区时发生异常: " + std::string(e.what()));
            return;
        }
        catch (...) {
            AddLogInfo(LogLevel::Warning, "[VMVision] 转换帧缓冲区时发生未知异常");
            return;
        }

        if (!newFrame.empty()) {
            // 每10秒记录一次日志
            auto currentTime = std::chrono::steady_clock::now();
            auto elapsedTime = std::chrono::duration_cast<std::chrono::seconds>(currentTime - vision->lastFrameLogTime_).count();
            if (elapsedTime >= 10) {
                // 删除冗余的收到新帧日志
                vision->lastFrameLogTime_ = currentTime;
            }

            // 使用写锁保护帧数据更新，独占访问
            try {
                std::unique_lock<std::shared_mutex> frameLock(vision->frameMutex_);
                // 再次检查是否请求停止处理，防止在获取锁期间状态发生变化
                auto currentClient = vision->client_.lock();
                if (vision->stopRequested_.load() || !currentClient) {
                    return;
                }
                vision->currentFrame_ = newFrame.clone();
                vision->newFrameAvailable_ = true;
            }
            catch (const std::exception& e) {
                AddLogInfo(LogLevel::Warning, "[VMVision] 更新帧数据时发生异常: " + std::string(e.what()));
                return;
            }
            catch (...) {
                AddLogInfo(LogLevel::Warning, "[VMVision] 更新帧数据时发生未知异常");
                return;
            }

            // 通知等待的线程有新帧可用
            try {
                vision->frameCV_.notify_one();
            }
            catch (...) {
                AddLogInfo(LogLevel::Warning, "[VMVision] 通知等待线程时发生异常");
            }
        }
    }
    catch (const std::exception& e) {
        // 记录异常但不中断程序
        AddLogInfo(LogLevel::Warning, "[VMVision] 帧更新回调中发生异常: " + std::string(e.what()));
    }
    catch (...) {
        AddLogInfo(LogLevel::Warning, "[VMVision] 帧更新回调中发生未知异常");
    }
}

// 將 rfb 幺緊衝轉換為 OpenCV Mat
cv::Mat VMVision::rfbToCvMat(rfbClient* client) {
    // 创建一个空的 Mat 作为默认返回值
    cv::Mat emptyMat;

    // 基本检查：停止请求、客户端有效性
    if (stopRequested_.load() || !client) {
        AddLogInfo(LogLevel::Warning, "[VMVision] " + vmName_ + " rfbToCvMat: 基本检查失败 - client为空或停止请求");
        return emptyMat;
    }

    // 检查client指针是否指向有效内存（简单检查）
    if (reinterpret_cast<uintptr_t>(client) < 0x1000) {
        AddLogInfo(LogLevel::Warning, "[VMVision] " + vmName_ + " rfbToCvMat: client指针无效");
        return emptyMat;
    }

    // 检查frameBuffer
    if (!client->frameBuffer) {
        AddLogInfo(LogLevel::Warning, "[VMVision] " + vmName_ + " rfbToCvMat: frameBuffer为空");
        return emptyMat;
    }

    try {
        // 在访问client成员之前，再次验证client指针
        auto currentClient = client_.lock();
        if (!currentClient) {
            AddLogInfo(LogLevel::Warning, "[VMVision] " + vmName_ + " rfbToCvMat: client指针已过期");
            return emptyMat;
        }

        // 获取帧缓冲区信息 - 使用临时变量避免多次访问可能损坏的指针
        void* frameBufferPtr = nullptr;
        int width = 0;
        int height = 0;
        int bitsPerPixel = 0;

        try {
            frameBufferPtr = client->frameBuffer;
            width = client->width;
            height = client->height;
            bitsPerPixel = client->format.bitsPerPixel;
        }
        catch (...) {
            AddLogInfo(LogLevel::Error, "[VMVision] " + vmName_ + " rfbToCvMat: 访问client成员时发生异常");
            return emptyMat;
        }

        // 检查获取的值是否有效
        if (!frameBufferPtr) {
            AddLogInfo(LogLevel::Warning, "[VMVision] " + vmName_ + " rfbToCvMat: frameBufferPtr为空");
            return emptyMat;
        }

        // 检查尺寸是否有效
        if (width <= 0 || height <= 0 || width > 10000 || height > 10000) {
            AddLogInfo(LogLevel::Warning, "[VMVision] " + vmName_ + " rfbToCvMat: 无效的帧尺寸: " +
                std::to_string(width) + "x" + std::to_string(height));
            return emptyMat;
        }

        // 检查bitsPerPixel是否有效
        if (bitsPerPixel != 24 && bitsPerPixel != 32) {
            AddLogInfo(LogLevel::Warning, "[VMVision] " + vmName_ + " rfbToCvMat: 不支持的像素格式: " +
                std::to_string(bitsPerPixel));
            return emptyMat;
        }

        // 创建矩阵并复制数据
        cv::Mat img;
        size_t dataSize = 0;

        if (bitsPerPixel == 32) {
            img = cv::Mat(height, width, CV_8UC4);
            dataSize = static_cast<size_t>(width) * static_cast<size_t>(height) * 4;
        }
        else if (bitsPerPixel == 24) {
            img = cv::Mat(height, width, CV_8UC3);
            dataSize = static_cast<size_t>(width) * static_cast<size_t>(height) * 3;
        }
        else {
            // 不支持的像素格式，尝试使用24位格式
            AddLogInfo(LogLevel::Warning, "[VMVision] " + vmName_ + " rfbToCvMat: 不支持的像素格式: " +
                std::to_string(bitsPerPixel) + "，尝试使用24位格式");
            img = cv::Mat(height, width, CV_8UC3);
            dataSize = static_cast<size_t>(width) * static_cast<size_t>(height) * 3;
        }

        // 检查矩阵是否创建成功
        if (!img.data) {
            AddLogInfo(LogLevel::Error, "[VMVision] " + vmName_ + " rfbToCvMat: 创建矩阵失败");
            return emptyMat;
        }

        // 复制像素数据
        try {
            memcpy(img.data, frameBufferPtr, dataSize);
        }
        catch (...) {
            AddLogInfo(LogLevel::Error, "[VMVision] " + vmName_ + " rfbToCvMat: 复制像素数据失败");
            return emptyMat;
        }

        // 简化的颜色空间转换
        if (bitsPerPixel == 32) {
            // 确保返回3通道BGR格式
            cv::Mat result;
            cv::cvtColor(img, result, cv::COLOR_BGRA2BGR);
            return result;
        }
        else {
            // 24位格式直接返回
    
            return img;
        }
    }
    catch (const cv::Exception& e) {
        AddLogInfo(LogLevel::Error, "[VMVision] " + vmName_ + " rfbToCvMat: OpenCV异常: " + e.what());
        return emptyMat;
    }
    catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "[VMVision] " + vmName_ + " rfbToCvMat: 标准异常: " + e.what());
        return emptyMat;
    }
    catch (...) {
        AddLogInfo(LogLevel::Error, "[VMVision] " + vmName_ + " rfbToCvMat: 未知异常");
        return emptyMat;
    }
}


// 准备图像用于模板匹配，确保两个图像格式兼容
bool VMVision::prepareImagesForMatching(const cv::Mat& searchRegion, const cv::Mat& templ,
    cv::Mat& processedSearchRegion, cv::Mat& processedTemplate) {
    try {
        // 检查输入图像是否为空
        if (searchRegion.empty()) {
            AddLogInfo(LogLevel::Error, "[VMVision] " + vmName_ + " 搜索区域为空");
            return false;
        }
        // 检查模板图像是否为空
        if (templ.empty()) {
            AddLogInfo(LogLevel::Error, "[VMVision] " + vmName_ + " 模板为空");
            return false;
        }

        // 确保图像连续存储在内存中这是许多 OpenCV 函数（包括 matchTemplate）高效运行的要求。
        if (!searchRegion.isContinuous()) {
            processedSearchRegion = searchRegion.clone();
            // 移除冗余的连续性检查日志
        }
        else {
            processedSearchRegion = searchRegion.clone();
        }
        // 确保图像连续存储在内存中这是许多 OpenCV 函数（包括 matchTemplate）高效运行的要求。
        if (!templ.isContinuous()) {
            processedTemplate = templ.clone();
            // 移除冗余的连续性检查日志
        }
        else {
            processedTemplate = templ.clone();
        }

        // 确保搜索区域和模板图像具有相同的通道数
        if (processedSearchRegion.channels() != processedTemplate.channels()) {
            if (processedSearchRegion.channels() == 3 && processedTemplate.channels() == 4) {
                cv::Mat temp;
                cv::cvtColor(processedTemplate, temp, cv::COLOR_BGRA2BGR);
                processedTemplate = temp;
                AddLogInfo(LogLevel::Debug, "[VMVision] " + vmName_ + " 将模板从4通道转换为3通道");
            }
            else if (processedSearchRegion.channels() == 4 && processedTemplate.channels() == 3) {
                cv::Mat temp;
                cv::cvtColor(processedSearchRegion, temp, cv::COLOR_BGRA2BGR);
                processedSearchRegion = temp;
                AddLogInfo(LogLevel::Debug, "[VMVision] " + vmName_ + " 将搜索区域从4通道转换为3通道");
            }
            else if (processedSearchRegion.channels() == 1 && processedTemplate.channels() > 1) {
                cv::Mat temp;
                cv::cvtColor(processedTemplate, temp, cv::COLOR_BGR2GRAY);
                processedTemplate = temp;
                AddLogInfo(LogLevel::Debug, "[VMVision] " + vmName_ + " 将模板转换为灰度图");
            }
            else if (processedSearchRegion.channels() > 1 && processedTemplate.channels() == 1) {
                cv::Mat temp;
                cv::cvtColor(processedSearchRegion, temp, cv::COLOR_BGR2GRAY);
                processedSearchRegion = temp;
                AddLogInfo(LogLevel::Debug, "[VMVision] " + vmName_ + " 将搜索区域转换为灰度图");
            }
            else {
                // 如果通道数不匹配且无法转换，则将两者都转为灰度图
                if (processedSearchRegion.channels() > 1) {
                    cv::Mat temp;
                    cv::cvtColor(processedSearchRegion, temp, cv::COLOR_BGR2GRAY);
                    processedSearchRegion = temp;
                    AddLogInfo(LogLevel::Debug, "[VMVision] " + vmName_ + " 将搜索区域转换为灰度图（强制）");
                }
                if (processedTemplate.channels() > 1) {
                    cv::Mat temp;
                    cv::cvtColor(processedTemplate, temp, cv::COLOR_BGR2GRAY);
                    processedTemplate = temp;
                    AddLogInfo(LogLevel::Debug, "[VMVision] " + vmName_ + " 将模板转换为灰度图（强制）");
                }
            }
        }

        // 确保两个图像都是8位无符号整数（CV_8U）
        if (processedSearchRegion.depth() != CV_8U) {
            cv::Mat temp;
            processedSearchRegion.convertTo(temp, CV_8U);
            processedSearchRegion = temp;
            AddLogInfo(LogLevel::Debug, "[VMVision] " + vmName_ + " 将搜索区域转换为8位无符号整数");
        }
        //强制将图像转换为 CV_8U
        if (processedTemplate.depth() != CV_8U) {
            cv::Mat temp;
            processedTemplate.convertTo(temp, CV_8U);
            processedTemplate = temp;
            AddLogInfo(LogLevel::Debug, "[VMVision] " + vmName_ + " 将模板转换为8位无符号整数");
        }

        // 再次检查图像是否为空
        if (processedSearchRegion.empty()) {
            AddLogInfo(LogLevel::Error, "[VMVision] " + vmName_ + " 处理后的搜索区域为空");
            return false;
        }

        if (processedTemplate.empty()) {
            AddLogInfo(LogLevel::Error, "[VMVision] " + vmName_ + " 处理后的模板为空");
            return false;
        }

        // 简化图像信息日志 - 仅在Debug级别输出
        AddLogInfo(LogLevel::Debug, "[VMVision] " + vmName_ + " 处理完成: 搜索区域" + 
                   std::to_string(processedSearchRegion.cols) + "x" + std::to_string(processedSearchRegion.rows) + 
                   ", 模板" + std::to_string(processedTemplate.cols) + "x" + std::to_string(processedTemplate.rows));

        // 检查图像尺寸
        int result_cols = processedSearchRegion.cols - processedTemplate.cols + 1;
        int result_rows = processedSearchRegion.rows - processedTemplate.rows + 1;
        //这是一个关键的预检查，防止 cv::matchTemplate 因尺寸不匹配而失败
        if (result_cols <= 0 || result_rows <= 0) {
            AddLogInfo(LogLevel::Error, "[VMVision] " + vmName_ + " 模板大小 (" + std::to_string(processedTemplate.cols) + "x" + std::to_string(processedTemplate.rows) + ") 超过搜索区域大小 (" + std::to_string(processedSearchRegion.cols) + "x" + std::to_string(processedSearchRegion.rows) + ")");
            return false;
        }

        return true;
    }
    catch (const cv::Exception& e) {
        AddLogInfo(LogLevel::Error, "[VMVision] " + vmName_ + " 图像处理时发生OpenCV异常: " + std::string(e.what()));
        return false;
    }
    catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "[VMVision] " + vmName_ + " 图像处理时发生异常: " + std::string(e.what()));
        return false;
    }
    catch (...) {
        AddLogInfo(LogLevel::Error, "[VMVision] " + vmName_ + " 图像处理时发生未知异常");
        return false;
    }
}



// 直接处理文本识别
void VMVision::processTextRecognizeDirect(cv::Rect roi, int sliceNumber, std::shared_ptr<std::promise<TextRecognizeResult>> resultPromise) {
    // 初始化OCR引擎（如果需要）
    if (!initializeOCRIfNeeded()) {
        AddLogInfo(LogLevel::Error, vmName_ + " [OCR] OCR引擎初始化失败");
        TextRecognizeResult res;
        res.success = false;
        res.errorMessage = "OCR引擎初始化失败";
        resultPromise->set_value(res);
        return;
    }

    cv::Mat searchFrame = getCurrentFrame();
    if (searchFrame.empty()) {
        AddLogInfo(LogLevel::Error, vmName_ + " [OCR] 屏幕帧为空.");
        TextRecognizeResult res;
        res.success = false;
        res.errorMessage = "屏幕帧为空";
        resultPromise->set_value(res);
        return;
    }

    cv::Mat img_roi;
    cv::Rect actualRoi = roi;
    
    // 检查并修正ROI边界
    if (actualRoi.width <= 0 || actualRoi.height <= 0 || 
        actualRoi.x < 0 || actualRoi.y < 0 ||
        actualRoi.x >= searchFrame.cols || actualRoi.y >= searchFrame.rows) {
        // 如果ROI无效，使用整个屏幕
        actualRoi = cv::Rect(0, 0, searchFrame.cols, searchFrame.rows);
        AddLogInfo(LogLevel::Warning, "[VMVision] " + vmName_ + " ROI参数无效，使用整个屏幕区域");
    } else {
        // 确保ROI不超出屏幕边界
        actualRoi.x = std::max(0, actualRoi.x);
        actualRoi.y = std::max(0, actualRoi.y);
        actualRoi.width = std::min(actualRoi.width, searchFrame.cols - actualRoi.x);
        actualRoi.height = std::min(actualRoi.height, searchFrame.rows - actualRoi.y);
        
        // 再次检查修正后的ROI
        if (actualRoi.width <= 0 || actualRoi.height <= 0) {
            actualRoi = cv::Rect(0, 0, searchFrame.cols, searchFrame.rows);
            AddLogInfo(LogLevel::Warning, "[VMVision] " + vmName_ + " ROI修正后仍无效，使用整个屏幕区域");
        }
    }
    
    if (actualRoi.width <= 0 || actualRoi.height <= 0) {
        AddLogInfo(LogLevel::Error, "[VMVision] " + vmName_ + " 屏幕帧宽高为0");
        TextRecognizeResult res;
        res.success = false;
        res.errorMessage = "屏幕帧宽高为0";
        resultPromise->set_value(res);
        return;
    }
    
    // 截取ROI区域
    img_roi = searchFrame(actualRoi);

    if (img_roi.empty()) {
        AddLogInfo(LogLevel::Error, vmName_ + " [OCR] ROI后的图片为空.");
        TextRecognizeResult res;
        res.success = false;
        res.errorMessage = "ROI后的图片为空";
        resultPromise->set_value(res);
        return;
    }

    try {

        // 检查是否正在停止
        if (stopRequested_.load()) {
            AddLogInfo(LogLevel::Warning, vmName_ + " [OCR] 正在停止，取消OCR识别");
            TextRecognizeResult res;
            res.success = false;
            res.errorMessage = "正在停止，取消OCR识别";
            resultPromise->set_value(res);
            return;
        }
        
        // 验证图像数据
        if (img_roi.empty() || img_roi.data == nullptr) {
            AddLogInfo(LogLevel::Error, vmName_ + " [OCR] ROI图像数据无效");
            TextRecognizeResult res;
            res.success = false;
            res.errorMessage = "ROI图像数据无效";
            resultPromise->set_value(res);
            return;
        }
        
        // 调用recognizeAllText进行文本识别
        // recognizeAllText内部会创建独立的OCR实例，因此无需传递OCR引用
        std::vector<PaddleOCR::OCRPredictResult> results;
        try {
            // 创建图像副本以确保线程安全
            cv::Mat safeImageCopy = img_roi.clone();
            // 直接调用修改后的recognizeAllText函数，不传递OCR引用
            results = recognizeAllText(safeImageCopy);
        } catch (const std::exception& e) {
            AddLogInfo(LogLevel::Error, vmName_ + " [OCR] 调用recognizeAllText异常: " + std::string(e.what()));
            TextRecognizeResult res;
            res.success = false;
            res.errorMessage = "OCR识别异常: " + std::string(e.what());
            resultPromise->set_value(res);
            return;
        } catch (...) {
            AddLogInfo(LogLevel::Error, "[VMVision] 调用recognizeAllText发生未知异常");
            TextRecognizeResult res;
            res.success = false;
            res.errorMessage = "OCR识别发生未知异常";
            resultPromise->set_value(res);
            return;
        }
        
        TextRecognizeResult finalResult;
        finalResult.success = true;
        
        // 处理识别结果
        for (const auto& result : results) {
            if (!result.text.empty()) {
                finalResult.recognizedTexts.push_back(result.text);
                
                // 计算文本位置（相对于ROI）
                cv::Rect textRect;
                if (!result.box.empty()) {
                    // 假设result.box包含文本的边界框信息
                    // 这里需要根据实际的PaddleOCR结果格式调整
                    textRect = cv::Rect(0, 0, img_roi.cols, img_roi.rows); // 临时使用整个ROI
                } else {
                    textRect = cv::Rect(0, 0, img_roi.cols, img_roi.rows);
                }
                
                // 转换为全图坐标
                cv::Point matchLocationInFullFrame = textRect.tl() + actualRoi.tl();
                cv::Point originalPoint = screenDistributor_->toOriginal(matchLocationInFullFrame, sliceNumber, searchFrame);
                cv::Rect finalRect(originalPoint.x, originalPoint.y, textRect.width, textRect.height);
                
                finalResult.textLocations.push_back(finalRect);
                // 使用默认置信度，因为OCRPredictResult可能没有confidence成员
                finalResult.confidences.push_back(0.5); // 默认置信度
            }
        }
        
        if (finalResult.recognizedTexts.empty()) {
            finalResult.success = false;
            finalResult.errorMessage = "未识别到任何文本";
        } else {
            finalResult.errorMessage = "成功识别到 " + std::to_string(finalResult.recognizedTexts.size()) + " 个文本";
            AddLogInfo(LogLevel::Info, vmName_ + " [OCR] 文本识别成功，识别到 " + std::to_string(finalResult.recognizedTexts.size()) + " 个文本");
        }
        
        resultPromise->set_value(finalResult);
    }
    catch (const cv::Exception& cvEx) {
        AddLogInfo(LogLevel::Error, vmName_ + " [OCR] OpenCV异常，在文本识别时." + std::string(cvEx.what()));
        TextRecognizeResult res;
        res.success = false;
        res.errorMessage = "OpenCV异常: " + std::string(cvEx.what());
        resultPromise->set_value(res);
    }
    catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, vmName_ + " [OCR] 在文本识别时的标准异常." + std::string(e.what()));
        TextRecognizeResult res;
        res.success = false;
        res.errorMessage = "标准异常: " + std::string(e.what());
        resultPromise->set_value(res);
    }
}

// 将切片坐标映射回原图坐标
// 参数：
// - slicePoint: 在切片中的坐标
// - sliceNumber: 切片编号（0-based）
// - imageWidth: 原始图像宽度
// - imageHeight: 原始图像高度
// - horizontal_slices: 横向切片数
// - vertical_slices: 纵向切片数
// - overlap_ratio: 重叠区域比例
// 返回：在原始图像中的坐标
cv::Point mapToOriginal(const cv::Point& slicePoint, int sliceNumber, int imageWidth, int imageHeight, 
    int horizontal_slices, int vertical_slices, float overlap_ratio) {
    // 计算切片尺寸
    int slice_width = imageWidth / horizontal_slices;
    int slice_height = imageHeight / vertical_slices;
    
    // 计算重叠区域大小
    int overlap_x = static_cast<int>(slice_width * overlap_ratio);
    int overlap_y = static_cast<int>(slice_height * overlap_ratio);
    
    // 计算切片的行列号
    int row = sliceNumber / horizontal_slices;
    int col = sliceNumber % horizontal_slices;
    
    // 计算切片在原图中的起始位置（考虑重叠）
    int start_x = std::max(0, col * slice_width - (col > 0 ? overlap_x : 0));
    int start_y = std::max(0, row * slice_height - (row > 0 ? overlap_y : 0));
    
    // 返回在原图中的坐标
    return cv::Point(slicePoint.x + start_x, slicePoint.y + start_y);
}
// 图像预处理函数实现
cv::Mat VMVision::preprocessImage(const cv::Mat& input) {
    if (input.empty()) {
        AddLogInfo(LogLevel::Error, "[VMVision] " + vmName_ + "OCR预处理错误：输入图像为空");
        return input;
    }

    cv::Mat result = input.clone();

    // 1. 轻度锐化 - 使用USM锐化(Unsharp Masking)
    cv::Mat blurred, sharpened;
    cv::GaussianBlur(result, blurred, cv::Size(0, 0), 3);
    cv::addWeighted(result, 1.5, blurred, -0.5, 0, sharpened);


    // 2. 轻度对比度增强
    cv::Mat enhanced;
    double alpha = 1.2; // 对比度因子 (1.0-3.0)
    int beta = 0;       // 亮度调整
    sharpened.convertTo(enhanced, -1, alpha, beta);

    return enhanced;
}

// 图像预处理函数
cv::Mat VMVision::preprocessImageMatch(const cv::Mat& input) {
    if (input.empty() || input.cols == 0 || input.rows == 0) {
        AddLogInfo(LogLevel::Error, "[VMVision] 尺寸为0，无法预处理");
        return input;
    }

    cv::Mat processed;
    if (input.channels() == 3) {
        cv::cvtColor(input, processed, cv::COLOR_BGR2GRAY);
    }
    else if (input.channels() == 1) {
        processed = input.clone();
    }
    else if (input.channels() == 4) {
        // 新增：支持4通道BGRA格式
        cv::cvtColor(input, processed, cv::COLOR_BGRA2GRAY);
        AddLogInfo(LogLevel::Info, "[VMVision] 将4通道BGRA图像转换为灰度图");
    }
    else {
        AddLogInfo(LogLevel::Error, "[VMVision] preprocessImageMatch 输入通道数不支持,请将图片改成jpg格式: " + std::to_string(input.channels()));
        return input;
    }
    
    // 确保输出为CV_8UC1格式
    if (processed.type() != CV_8UC1) {
        processed.convertTo(processed, CV_8UC1);
    }
    
    if (processed.cols < 3 || processed.rows < 3) {
        AddLogInfo(LogLevel::Error, "[VMVision] preprocessImageMatch 输入尺寸过小: " + std::to_string(processed.cols) + "x" + std::to_string(processed.rows));
        return processed;
    }
    
    cv::equalizeHist(processed, processed);
    cv::GaussianBlur(processed, processed, cv::Size(3, 3), 0);
    return processed;
}

// 找到文本的位置信息（中心点、置信度、边界框
std::tuple<cv::Point, float> findTextLocation(PaddleOCR::SimpleOCR& ocr, const cv::Mat& image, const std::string& text, const std::string& vmName)
{
    AddLogInfo(LogLevel::Info, "[VMVision] " + vmName + "[ocr] 查找文本" + text);

    // 设置使用识别的配置 - 使用高召回率配置提高文本检测率
    ocr.setParamConfig(PaddleOCR::SimpleOCR::CONFIG_HIGH_RECALL);

    // 根据图像大小决定是否需要二次切片
    const int SLICE_THRESHOLD = 800;
    cv::Point bestMatchPoint(-1, -1);
    float bestConfidence = 0.0f;

    if (image.cols < SLICE_THRESHOLD && image.rows < SLICE_THRESHOLD) {
        // 情况1：宽度和高度都小于800，直接识别
        AddLogInfo(LogLevel::Debug, "[VMVision] " + vmName + " ROI尺寸较小(" + 
                   std::to_string(image.cols) + "x" + std::to_string(image.rows) + 
                   ")，直接识别，无需二次切片");
        
        auto result = ocr.findText(image, text);
        bestMatchPoint = result.first;
        bestConfidence = result.second;
        
        // 坐标转换：ROI坐标 -> 切片坐标（无变化，因为image就是ROI图像）
        // 坐标已经是基于ROI的，直接返回
    }
    else if (image.cols >= SLICE_THRESHOLD && image.rows < SLICE_THRESHOLD) {
        // 情况2：宽度>=800，高度<800，水平二次切片
        AddLogInfo(LogLevel::Debug, "[VMVision] " + vmName + " ROI宽度较大(" + 
                   std::to_string(image.cols) + "x" + std::to_string(image.rows) + 
                   ")，进行水平二次切片");
        
        int numHorizontalSlices = 2;
        int sliceWidth = image.cols / numHorizontalSlices;
        int overlap = std::min(100, sliceWidth / 10); // 10%重叠或最大100像素
        
        for (int i = 0; i < numHorizontalSlices; i++) {
            int startX = std::max(0, i * sliceWidth - (i > 0 ? overlap : 0));
            int endX = std::min(image.cols, (i + 1) * sliceWidth + (i < numHorizontalSlices - 1 ? overlap : 0));
            int width = endX - startX;
            
            cv::Rect sliceRect(startX, 0, width, image.rows);
            cv::Mat sliceImage = image(sliceRect);
            
            auto result = ocr.findText(sliceImage, text);
            if (result.second > bestConfidence) {
                bestConfidence = result.second;
                // 坐标转换：二次切片坐标 -> ROI坐标
                bestMatchPoint = cv::Point(result.first.x + startX, result.first.y);
            }
        }
    }
    else if (image.cols < SLICE_THRESHOLD && image.rows >= SLICE_THRESHOLD) {
        // 情况3：宽度<800，高度>=800，垂直二次切片
        AddLogInfo(LogLevel::Debug, "[VMVision] " + vmName + " ROI高度较大(" + 
                   std::to_string(image.cols) + "x" + std::to_string(image.rows) + 
                   ")，进行垂直二次切片");
        
        int numVerticalSlices = 2;
        int sliceHeight = image.rows / numVerticalSlices;
        int overlap = std::min(100, sliceHeight / 10); // 10%重叠或最大100像素
        
        for (int i = 0; i < numVerticalSlices; i++) {
            int startY = std::max(0, i * sliceHeight - (i > 0 ? overlap : 0));
            int endY = std::min(image.rows, (i + 1) * sliceHeight + (i < numVerticalSlices - 1 ? overlap : 0));
            int height = endY - startY;
            
            cv::Rect sliceRect(0, startY, image.cols, height);
            cv::Mat sliceImage = image(sliceRect);
            
            auto result = ocr.findText(sliceImage, text);
            if (result.second > bestConfidence) {
                bestConfidence = result.second;
                // 坐标转换：二次切片坐标 -> ROI坐标
                bestMatchPoint = cv::Point(result.first.x, result.first.y + startY);
            }
        }
    }
    else {
        // 情况4：宽度>=800，高度>=800，四分切片
        AddLogInfo(LogLevel::Debug, "[VMVision] " + vmName + " ROI尺寸较大(" + 
                   std::to_string(image.cols) + "x" + std::to_string(image.rows) + 
                   ")，进行四分二次切片");
        
        int numHorizontalSlices = 2;
        int numVerticalSlices = 2;
        int sliceWidth = image.cols / numHorizontalSlices;
        int sliceHeight = image.rows / numVerticalSlices;
        int overlapX = std::min(100, sliceWidth / 10);
        int overlapY = std::min(100, sliceHeight / 10);
        
        for (int y = 0; y < numVerticalSlices; y++) {
            for (int x = 0; x < numHorizontalSlices; x++) {
                int startX = std::max(0, x * sliceWidth - (x > 0 ? overlapX : 0));
                int endX = std::min(image.cols, (x + 1) * sliceWidth + (x < numHorizontalSlices - 1 ? overlapX : 0));
                int startY = std::max(0, y * sliceHeight - (y > 0 ? overlapY : 0));
                int endY = std::min(image.rows, (y + 1) * sliceHeight + (y < numVerticalSlices - 1 ? overlapY : 0));
                
                cv::Rect sliceRect(startX, startY, endX - startX, endY - startY);
                cv::Mat sliceImage = image(sliceRect);
                
                auto result = ocr.findText(sliceImage, text);
                if (result.second > bestConfidence) {
                    bestConfidence = result.second;
                    // 坐标转换：二次切片坐标 -> ROI坐标
                    bestMatchPoint = cv::Point(result.first.x + startX, result.first.y + startY);
                }
            }
        }
    }

    std::tuple<cv::Point, float> result_tuple;
    // 如果找到匹配
    if (bestConfidence > 0 && bestMatchPoint.x >= 0 && bestMatchPoint.y >= 0) {
        AddLogInfo(LogLevel::Info, "[VMVision] " + vmName + "识别" + text + "[ocr] 最佳匹配结果: (" + 
                   std::to_string(bestMatchPoint.x) + "," + std::to_string(bestMatchPoint.y) + 
                   ") 置信度: " + std::to_string(bestConfidence));
        
        result_tuple = std::make_tuple(bestMatchPoint, bestConfidence);
    } else {
        AddLogInfo(LogLevel::Info, "[VMVision] " + vmName + " 未找到文本 '" + text + "'");
        result_tuple = std::make_tuple(cv::Point(-1, -1), 0.0f);
    }

    return result_tuple;
}

// 函数：识别图片中的所有文本
std::vector<PaddleOCR::OCRPredictResult> VMVision::recognizeAllText(const cv::Mat& image) {

    std::vector<PaddleOCR::OCRPredictResult> results;
    
    try {
        // 验证输入图像
        if (image.empty()) {
            AddLogInfo(LogLevel::Error, "[VMVision] OCR输入图像为空");
            return results;
        }
        
        if (image.cols <= 0 || image.rows <= 0) {
            AddLogInfo(LogLevel::Error, "[VMVision] OCR输入图像尺寸无效: " + 
                       std::to_string(image.cols) + "x" + std::to_string(image.rows));
            return results;
        }
        
        // 预处理图像
        cv::Mat preprocessedFrame;
        try {
            preprocessedFrame = preprocessImage(image);
            if (preprocessedFrame.empty()) {
                AddLogInfo(LogLevel::Error, "[VMVision] 图像预处理后为空");
                return results;
            }
        } catch (const std::exception& e) {
            AddLogInfo(LogLevel::Error, "[VMVision] 图像预处理异常: " + std::string(e.what()));
            return results;
        }
        
        // 为了避免PaddleOCR recognize()方法的重复调用问题，每次创建新的OCR引擎实例
        // 这样可以确保每次识别都使用干净的引擎状态，无需检查共享引擎状态
        try {
            // 删除冗余的OCR引擎创建日志
            PaddleOCR::SimpleOCR recognizeOcr;
            results = recognizeOcr.recognize(preprocessedFrame);
            // 删除冗余的OCR引擎销毁日志
        } catch (const std::exception& e) {
            AddLogInfo(LogLevel::Error, "[VMVision] OCR识别异常: " + std::string(e.what()));
            return std::vector<PaddleOCR::OCRPredictResult>(); // 返回空结果
        } catch (...) {
            AddLogInfo(LogLevel::Error, "[VMVision] OCR识别发生未知异常");
            return std::vector<PaddleOCR::OCRPredictResult>(); // 返回空结果
        }
        
        // 处理识别结果
        for (const auto& result : results) {
            try {
                // 获取文本中心点
                cv::Point center = PaddleOCR::GetCenterPoint(result);

                AddLogInfo(LogLevel::Info, result.text);
                AddLogInfo(LogLevel::Info, std::to_string(result.score));
                AddLogInfo(LogLevel::Info, " Center:" + std::to_string(center.x) + " " + std::to_string(center.y));
            } catch (const std::exception& e) {
                AddLogInfo(LogLevel::Warning, "[VMVision] 处理OCR结果时异常: " + std::string(e.what()));
                // 继续处理其他结果
            } catch (...) {
                AddLogInfo(LogLevel::Warning, "[VMVision] 处理OCR结果时发生未知异常");
                // 继续处理其他结果
            }
        }
        
    } catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "[VMVision] recognizeAllText整体异常: " + std::string(e.what()));
        return std::vector<PaddleOCR::OCRPredictResult>(); // 返回空结果
    } catch (...) {
        AddLogInfo(LogLevel::Error, "[VMVision] recognizeAllText发生未知异常");
        return std::vector<PaddleOCR::OCRPredictResult>(); // 返回空结果
    }
    
    return results;
}

// 检查连接状态
bool VMVision::checkConnectionStatus() {

    // 默认假设连接正常
    bool connectionOk = true;

    // 检查是否正在停止过程中
    if (!s_allowFrameUpdates.load()) {
        AddLogInfo(LogLevel::Warning, "[VMVision] 全局帧更新标志已禁用，可能正在停止过程中");
        return false;
    }

    // 检查客户端是否有效
    auto currentClient = client_.lock();
    if (!currentClient) {
        AddLogInfo(LogLevel::Warning, "[VMVision] 客户端指针为空");
        connectionOk = false;
    } else if (!currentClient->frameBuffer) {
        AddLogInfo(LogLevel::Warning, "[VMVision] 客户端帧缓冲区无效");
        connectionOk = false;
    }

    // 检查当前帧是否有效
    {
        // 使用共享锁（读锁）来读取帧数据，允许多个读取操作并发
        std::shared_lock<std::shared_mutex> frameLock(frameMutex_);
        if (currentFrame_.empty()) {
            connectionOk = false;

            // 释放共享锁，准备获取独占锁
            frameLock.unlock();

            // 如果客户端和帧缓冲区有效，尝试获取一帧
            auto currentClient = client_.lock();
            if (currentClient && currentClient->frameBuffer) {
                try {
                    cv::Mat newFrame = rfbToCvMat(currentClient.get());
                    if (!newFrame.empty()) {
                        // 获取独占锁来更新帧数据
                        std::unique_lock<std::shared_mutex> writeFrameLock(frameMutex_);
                        currentFrame_ = newFrame.clone();
                        AddLogInfo(LogLevel::Info, "[VMVision] 成功主动获取了新帧");
                        connectionOk = true; // 获取成功，连接正常
                    }
                    else {
                        AddLogInfo(LogLevel::Error, "[VMVision] 主动获取帧失败");
                    }
                } catch (const std::exception& e) {
                    AddLogInfo(LogLevel::Error, "[VMVision] 获取帧时发生异常: " + std::string(e.what()));
                    connectionOk = false;
                } catch (...) {
                    AddLogInfo(LogLevel::Error, "[VMVision] 获取帧时发生未知异常");
                    connectionOk = false;
                }
            }
        }
    }

    if (!connectionOk) {
        AddLogInfo(LogLevel::Error, "[VMVision] 连接状态异常");
    }

    return connectionOk;
}

// 获取当前帧的函数 - 供线程池任务使用
cv::Mat VMVision::getCurrentFrame() {
    
    // 首先尝试使用共享锁（读锁）来读取帧数据
    {
        // 删除冗余的共享锁读取帧数据日志
        std::shared_lock<std::shared_mutex> frameLock(frameMutex_);
        if (!currentFrame_.empty()) {
            return currentFrame_.clone(); // 如果帧不为空，直接返回副本
        }
    }

    // 如果当前帧为空，尝试直接从客户端获取，需要使用写锁
    std::unique_lock<std::shared_mutex> frameLock(frameMutex_);
    if (currentFrame_.empty()) {
        auto currentClient = client_.lock();
        if (currentClient && currentClient->frameBuffer) {
            try {
                cv::Mat frame = rfbToCvMat(currentClient.get());
                if (!frame.empty()) {
                    currentFrame_ = frame.clone();
                }
            } catch (const std::exception& e) {
                AddLogInfo(LogLevel::Error, "[VMVision] 获取当前帧时发生异常: " + std::string(e.what()));
            } catch (...) {
                AddLogInfo(LogLevel::Error, "[VMVision] 获取当前帧时发生未知异常");
            }
        }
    }
    return currentFrame_.clone(); // 返回当前帧的副本，避免外部修改
}


// ==========================================================================================
// ================================ 切片配置管理 ======================================
// ==========================================================================================

// 设置切片配置
void VMVision::setSliceConfiguration(int rows, int cols) {
    // 使用 ScreenFrameDistributor 的验证函数
    if (!ScreenFrameDistributor::isValidSliceConfiguration(rows, cols)) {
        AddLogInfo(LogLevel::Error, "[VMVision] " + vmName_ + ": " + 
                   ScreenFrameDistributor::getSliceConfigurationDescription(rows, cols));
        return;
    }
    
    if (screenDistributor_) {
        screenDistributor_->setGridSize(rows, cols);

        AddLogInfo(LogLevel::Info, "[VMVision] " + vmName_ + ": 切片配置已更新为 " +
                   ScreenFrameDistributor::getSliceConfigurationDescription(rows, cols));

        // 输出详细的切片配置信息
        AddLogInfo(LogLevel::Debug, "[VMVision] " + vmName_ + ": " + screenDistributor_->getConfigurationInfo());
    } else {
        AddLogInfo(LogLevel::Error, "[VMVision] " + vmName_ + ": 屏幕切片器未初始化");
    }
}

// 获取切片配置
std::pair<int, int> VMVision::getSliceConfiguration() const {
    if (screenDistributor_) {
        return std::make_pair(screenDistributor_->getRows(), screenDistributor_->getCols());
    } else {
        AddLogInfo(LogLevel::Warning, "[VMVision] " + vmName_ + ": 屏幕切片器未初始化，返回默认配置");
        return std::make_pair(1, 2); // 返回默认配置
    }
}

// 获取ScreenFrameDistributor实例
ScreenFrameDistributor* VMVision::getScreenFrameDistributor() const {
    return screenDistributor_.get();
}

// 初始化 OCR 引擎 - 每次都重新初始化以确保线程安全
bool VMVision::initializeOCRIfNeeded() {
    // 使用互斥锁保护 OCR 引擎的初始化过程
    std::lock_guard<std::mutex> lock(ocrMutex_);

    // ✅ 强制每次都重新初始化OCR引擎，避免多线程竞争导致的状态损坏
    try {

        
        // 先释放旧引擎（如果有的话）
        if (ocrEngine_) {
            ocrEngine_.reset();
        }
        
        // 创建新引擎
        ocrEngine_ = std::make_unique<PaddleOCR::SimpleOCR>();
        
        if (!ocrEngine_) {
            AddLogInfo(LogLevel::Error, "[VMVision] " + vmName_ + " OCR引擎创建失败");
            ocrInitialized_.store(false);
            return false;
        }
        
        ocrInitialized_.store(true);
        return true;
        
    } catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "[VMVision] " + vmName_ + " OCR引擎初始化异常: " + std::string(e.what()));
        ocrInitialized_.store(false);
        return false;
    } catch (...) {
        AddLogInfo(LogLevel::Error, "[VMVision] " + vmName_ + " OCR引擎初始化发生未知异常");
        ocrInitialized_.store(false);
        return false;
    }
}

// 🔧 新增：直接处理模板匹配 - 移除中间层
void VMVision::processTemplateMatchDirect(const std::string& templateName, double threshold, cv::Rect roi, int sliceNumber, std::shared_ptr<std::promise<std::pair<bool, cv::Point>>> resultPromise) {
    try {
        // 获取当前帧和切片
        cv::Mat frame = getCurrentFrame();
        if (frame.empty()) {
            AddLogInfo(LogLevel::Error, "[VMVision] " + vmName_ + " 无法获取帧数据进行匹配");
            resultPromise->set_value({ false, cv::Point(-1, -1) });
            return;
        }

        cv::Mat slice = screenDistributor_->getSliceImage(frame, sliceNumber);
        if (slice.empty()) {
            AddLogInfo(LogLevel::Error, "[VMVision] " + vmName_ + " 无法获取切片图像");
            resultPromise->set_value({ false, cv::Point(-1, -1) });
            return;
        }

        // 获取模板
        // 打印当前工作目录
        cv::Mat templ = TemplatePathManager::getInstance().getTemplate(templateName);
        if (templ.empty()) {
            AddLogInfo(LogLevel::Error, "[VMVision] " + vmName_ + " 模板 '" + templateName + "' 为空.");
            resultPromise->set_value({ false, cv::Point(-1, -1) });
            return;
        }

        // 处理ROI
        cv::Rect actualRoi = roi;
        if (actualRoi.width <= 0 && actualRoi.height <= 0) {
            actualRoi = cv::Rect(0, 0, slice.cols, slice.rows);
        }
        
        cv::Mat img_roi = slice(actualRoi);
        if (img_roi.empty() || img_roi.cols < templ.cols || img_roi.rows < templ.rows) {
            AddLogInfo(LogLevel::Error, "[VMVision] " + vmName_ + " ROI区域无效或小于模板尺寸");
            resultPromise->set_value({ false, cv::Point(-1, -1) });
            return;
        }

        // 预处理
        cv::Mat img_processed = preprocessImageMatch(img_roi);
        cv::Mat templ_processed = preprocessImageMatch(templ);
        
        // 确保类型一致
        if (img_processed.type() != templ_processed.type()) {
            img_processed.convertTo(img_processed, CV_8UC1);
            templ_processed.convertTo(templ_processed, CV_8UC1);
        }

        // ✅ 在模板匹配前检查切片状态，防止已暂停的切片继续执行
        auto* stateManager = VMStateManager::getInstance();
        if (stateManager && stateManager->isSlicePaused(vmName_, sliceNumber)) {
            AddLogInfo(LogLevel::Debug, vmName_ + " [Vision] 切片 " + std::to_string(sliceNumber) + " 已暂停，取消模板匹配执行");
            resultPromise->set_value({ false, cv::Point(-1, -1) });
            return;
        }

        // 模板匹配
        cv::Mat result;
        cv::matchTemplate(img_processed, templ_processed, result, cv::TM_CCOEFF_NORMED);

        double minVal, maxVal;
        cv::Point minLoc, maxLoc;
        cv::minMaxLoc(result, &minVal, &maxVal, &minLoc, &maxLoc);

        if (static_cast<float>(maxVal) >= threshold) {
            // 计算匹配中心点
            cv::Point matchLocationInROI = maxLoc;
            cv::Point matchLocationInSlice = matchLocationInROI + actualRoi.tl();
            cv::Point centerPoint;
            centerPoint.x = matchLocationInSlice.x + templ_processed.cols / 2;
            centerPoint.y = matchLocationInSlice.y + templ_processed.rows / 2;

            AddLogInfo(LogLevel::Info, "[VMVision] " + vmName_ + " Template '" + templateName + "' matched. Confidence: " +
                std::to_string(maxVal) + ", 切片内中心: (" + std::to_string(centerPoint.x) + ", " + std::to_string(centerPoint.y) + ")");
            resultPromise->set_value({ true, centerPoint });
        } else {
            AddLogInfo(LogLevel::Info, "[VMVision] " + vmName_ + " Template '" + templateName + "' not found (Confidence " + std::to_string(maxVal) + " < Threshold " + std::to_string(threshold) + ")");
            resultPromise->set_value({ false, cv::Point(-1, -1) });
        }
    }
    catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "[VMVision] " + vmName_ + " 模板匹配异常: " + std::string(e.what()));
        resultPromise->set_value({ false, cv::Point(-1, -1) });
    }
}

// 🔧 新增：直接处理文本匹配 - 移除中间层
void VMVision::processTextMatchDirect(const std::string& wordsToMatch, double threshold, cv::Rect roi, int sliceNumber, std::shared_ptr<std::promise<std::pair<bool, cv::Point>>> resultPromise) {
    try {
        // 初始化OCR引擎
        if (!initializeOCRIfNeeded()) {
            AddLogInfo(LogLevel::Error, vmName_ + " [OCR] OCR引擎初始化失败");
            resultPromise->set_value({ false, cv::Point(-1, -1) });
            return;
        }

        // 获取当前帧和切片
        cv::Mat frame = getCurrentFrame();
        if (frame.empty()) {
            AddLogInfo(LogLevel::Error, vmName_ + " [OCR] 无法获取帧数据");
            resultPromise->set_value({ false, cv::Point(-1, -1) });
            return;
        }

        cv::Mat slice = screenDistributor_->getSliceImage(frame, sliceNumber);
        if (slice.empty()) {
            AddLogInfo(LogLevel::Error, vmName_ + " [OCR] 无法获取切片图像");
            resultPromise->set_value({ false, cv::Point(-1, -1) });
            return;
        }

        // 处理ROI
        cv::Rect actualRoi = roi;
        if (actualRoi.width <= 0 || actualRoi.height <= 0 || 
            actualRoi.x < 0 || actualRoi.y < 0 ||
            actualRoi.x >= slice.cols || actualRoi.y >= slice.rows) {
            actualRoi = cv::Rect(0, 0, slice.cols, slice.rows);
            AddLogInfo(LogLevel::Warning, "[VMVision] " + vmName_ + " ROI参数无效，使用整个切片区域");
        } else {
            // 确保ROI不超出切片边界
            actualRoi.x = std::max(0, actualRoi.x);
            actualRoi.y = std::max(0, actualRoi.y);
            actualRoi.width = std::min(actualRoi.width, slice.cols - actualRoi.x);
            actualRoi.height = std::min(actualRoi.height, slice.rows - actualRoi.y);
        }
        
        cv::Mat img_roi = slice(actualRoi);
        if (img_roi.empty()) {
            AddLogInfo(LogLevel::Error, vmName_ + " [OCR] ROI后的图片为空.");
            resultPromise->set_value({ false, cv::Point(-1, -1) });
            return;
        }

        // 🔧 简化的文本识别
        std::tuple<cv::Point, float> results;
        {
            std::lock_guard<std::mutex> ocrLock(ocrMutex_);
            if (!ocrEngine_) {
                AddLogInfo(LogLevel::Error, vmName_ + " [OCR] OCR引擎在使用时为空");
                resultPromise->set_value({ false, cv::Point(-1, -1) });
                return;
            }

            // ✅ 在OCR执行前再次检查切片状态，防止已暂停的切片继续执行
            auto* stateManager = VMStateManager::getInstance();
            if (stateManager && stateManager->isSlicePaused(vmName_, sliceNumber)) {
                AddLogInfo(LogLevel::Debug, vmName_ + " [OCR] 切片 " + std::to_string(sliceNumber) + " 已暂停，取消OCR执行");
                resultPromise->set_value({ false, cv::Point(-1, -1) });
                return;
            }

            // 🔧 简化版本：直接调用OCR，移除复杂的二次切片逻辑
            auto result = ocrEngine_->findText(img_roi, wordsToMatch);
            results = std::make_tuple(result.first, result.second);
        }

        float confidence = std::get<1>(results);
        bool found = confidence >= threshold;
        cv::Point location = std::get<0>(results);

        if (found) {
            // 转换为切片坐标
            cv::Point matchLocationInSlice = location + actualRoi.tl();
            resultPromise->set_value({ found, matchLocationInSlice });
            AddLogInfo(LogLevel::Info, vmName_ + " [OCR] 文本 '" + wordsToMatch + "' 已找到，切片内坐标: (" + 
                       std::to_string(matchLocationInSlice.x) + ", " + std::to_string(matchLocationInSlice.y) + 
                       ") 置信度: " + std::to_string(confidence));
        } else {
            resultPromise->set_value({ false, cv::Point(-1, -1) });
            AddLogInfo(LogLevel::Info, vmName_ + " [OCR] 文本 '" + wordsToMatch + "' 没有找到. 置信度: " + std::to_string(confidence) + ", 阈值: " + std::to_string(threshold));
        }
    }
    catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, vmName_ + " [OCR] 文本匹配异常: " + std::string(e.what()));
        resultPromise->set_value({ false, cv::Point(-1, -1) });
    }
}


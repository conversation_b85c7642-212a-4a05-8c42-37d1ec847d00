// - Introduction, links and more at the top of imgui.cpp

#include "imgui.h"
#include "imgui_impl_win32.h"
#include "imgui_impl_dx11.h"
#include <d3d11.h>
#include <tchar.h>
#include <windows.h>
#include <GUI/mainui.h>
#include <GUI/hardwareinfo/hardwareinfo.h>
#include <GUI/LoadingManager.h>
#include <string>
#include <thread>
#include <iostream>
#include <chrono>
#include <GUI/consolelog/consolelog.h>
#include <Core/VersionChecker.h>
#include <Core/UnifiedThreadManager.h>
#include <Core/ConfigManager.h>

// 全局变量
std::string g_hardware_OS;
std::string g_hardware_CPU;
std::string g_hardware_MOTHER;
std::string g_hardware_GPU;
std::string g_hardware_MEM;

// 关闭确认对话框相关变量
static bool g_showExitConfirmDialog = false;
static bool g_shouldExit = false;

static ID3D11Device* g_pd3dDevice = nullptr;
static ID3D11DeviceContext* g_pd3dDeviceContext = nullptr;
static IDXGISwapChain* g_pSwapChain = nullptr;
static ID3D11RenderTargetView* g_mainRenderTargetView = nullptr;

// 前向声明
bool CreateDeviceD3D(HWND hWnd);
void CleanupDeviceD3D();
void CreateRenderTarget();
void CleanupRenderTarget();
LRESULT WINAPI WndProc(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam);

// Main code
int APIENTRY WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow)
{
    // 设置工作目录为可执行文件所在目录
    WCHAR exePath[MAX_PATH];
    GetModuleFileNameW(nullptr, exePath, MAX_PATH);
    WCHAR* lastBackslash = wcsrchr(exePath, L'\\');
    if (lastBackslash != nullptr) {
        *lastBackslash = L'\0';  // 截断文件名，只保留路径
        SetCurrentDirectoryW(exePath);
    }

    // 首先启动线程安全的日志系统
    LogManager::getInstance()->startup();
    
    // 添加日志记录当前工作目录
    char currentDir[MAX_PATH];
    GetCurrentDirectoryA(MAX_PATH, currentDir);
    AddLogInfo(LogLevel::Info, std::string("[模板加载] 当前工作目录: ") + currentDir);
    // 初始化配置管理器
    auto& configManager = ConfigManager::getInstance();
    
    // 先初始化配置管理器（这会自动加载配置目录中的所有JSON文件）
    if (!configManager.initialize("./config", true)) {
        MessageBoxW(nullptr, L"配置管理器初始化失败", L"配置错误", MB_OK | MB_ICONERROR);
        return 1;
    }
    
    // 版本检查 - 必须在应用启动前完成
    VersionChecker::CheckResult checkResult = VersionChecker::checkVersion();
    if (checkResult != VersionChecker::CheckResult::SUCCESS) {
        std::string errorMessage = VersionChecker::getFormattedErrorMessage(checkResult);
        
        // 转换为宽字符串用于Windows消息框
        int size_needed = MultiByteToWideChar(CP_UTF8, 0, errorMessage.c_str(), (int)errorMessage.size(), nullptr, 0);
        std::wstring wErrorMessage(size_needed, 0);
        MultiByteToWideChar(CP_UTF8, 0, errorMessage.c_str(), (int)errorMessage.size(), &wErrorMessage[0], size_needed);
        
        MessageBoxW(nullptr, wErrorMessage.c_str(), L"版本验证失败", MB_OK | MB_ICONERROR);
        
        return 1;  // 退出应用程序
    }
    
    
    // 初始化加载管理器
    LoadingManager& loadingManager = LoadingManager::getInstance();
    loadingManager.initialize();
    
    // 设置最小显示时间为5秒，确保UI完全准备好
    loadingManager.setMinDisplayTime(5000);
    
    // 可选：设置UI准备完成检测回调（用于更精确的UI准备检测）
    loadingManager.setUIReadyCallback([]() {
        // 这里可以添加更复杂的UI准备检测逻辑
        // 例如：检查所有UI组件是否已加载完成
        // 目前简单返回true，表示UI已准备完成
        return true;
    });
    
    // 从配置获取窗口设置
    std::string windowTitle = configManager.getConfig<std::string>("system.window_title", "游戏脚本自动化工具");
    int windowWidth = configManager.getConfig<int>("system.main_window_width", 1440);
    int windowHeight = configManager.getConfig<int>("system.main_window_height", 900);
    
    // 转换窗口标题为宽字符串
    int titleSize = MultiByteToWideChar(CP_UTF8, 0, windowTitle.c_str(), -1, nullptr, 0);
    std::wstring wWindowTitle(titleSize, 0);
    MultiByteToWideChar(CP_UTF8, 0, windowTitle.c_str(), -1, &wWindowTitle[0], titleSize);
    
    // 创建窗口
    WNDCLASSEXW wc = { sizeof(wc), CS_CLASSDC, WndProc, 0L, 0L, GetModuleHandle(nullptr), nullptr, nullptr, nullptr, nullptr, L"qnyh_auto", nullptr };

    ::RegisterClassExW(&wc);
    HWND hwnd = ::CreateWindowW(wc.lpszClassName, wWindowTitle.c_str(), WS_OVERLAPPEDWINDOW, 100, 100, windowWidth, windowHeight, nullptr, nullptr, wc.hInstance, nullptr);

	// 初始化Direct3D
    if (!CreateDeviceD3D(hwnd))
    {
        CleanupDeviceD3D();
        ::UnregisterClassW(wc.lpszClassName, wc.hInstance);
        return 1;
    }

	// 显示窗口
    ::ShowWindow(hwnd, SW_SHOWDEFAULT);
    ::UpdateWindow(hwnd);
    
    // 初始化ImGui
    IMGUI_CHECKVERSION();
    ImGui::CreateContext();
    ImGui_ImplWin32_Init(hwnd);
    ImGui_ImplDX11_Init(g_pd3dDevice, g_pd3dDeviceContext);

    // 设置imgui风格
    ImGui::StyleColorsDark();

    // 加载字体
    ImGuiIO& io = ImGui::GetIO(); (void)io;
    ImFont* font = io.Fonts->AddFontFromFileTTF("c:\\Windows\\Fonts\\simhei.ttf", 18.0f, nullptr, io.Fonts->GetGlyphRangesChineseFull());
    
    if (font == nullptr) {
        font = io.Fonts->AddFontFromFileTTF("c:\\Windows\\Fonts\\msyh.ttf", 18.0f, nullptr, io.Fonts->GetGlyphRangesChineseFull());
    }
    if (font == nullptr) {
        font = io.Fonts->AddFontFromFileTTF("c:\\Windows\\Fonts\\simsun.ttc", 18.0f, nullptr, io.Fonts->GetGlyphRangesChineseFull());
    }
    if (font == nullptr) {
        AddLogInfo(LogLevel::Warning, "[主程序] 无法加载中文字体，使用默认字体");
        font = io.Fonts->AddFontDefault();
    }
    
    if (font != nullptr) {
        io.FontDefault = font;
    } else {
        AddLogInfo(LogLevel::Error, "[主程序] 字体加载失败，使用系统默认字体");
    }
    
    // 添加加载步骤 - 在主循环中执行
    loadingManager.addStep("版本检查", "正在验证软件版本...", 0.1f);
    loadingManager.addStep("初始化DirectX", "正在初始化DirectX图形引擎...", 0.2f);
    loadingManager.addStep("加载中文字体", "正在加载中文字体支持...", 0.3f);
    loadingManager.addStep("获取硬件信息", "正在获取系统硬件信息...", 0.5f);
    loadingManager.addStep("加载配置文件", "正在加载系统配置文件...", 0.7f);
    loadingManager.addStep("初始化VNC模块", "正在初始化VNC连接模块...", 0.8f);
    loadingManager.addStep("初始化视觉识别", "正在初始化计算机视觉模块...", 0.9f);
    loadingManager.addStep("完成初始化", "正在完成最后的初始化步骤...", 1.0f);
    
    // 使用统一线程管理器启动硬件信息获取线程
    std::atomic<bool> hardwareInfoComplete(false);
    auto& threadManager = UnifiedThreadManager::getInstance();
    
    // 初始化线程管理器
    if (!threadManager.initialize()) {
        MessageBoxW(nullptr, L"线程管理器初始化失败", L"初始化错误", MB_OK | MB_ICONERROR);
        return 1;
    }
    std::string hardwareThreadResult = threadManager.createNamedThreadOnce("HardwareInfo", [&]() {
        try {
            std::this_thread::sleep_for(std::chrono::milliseconds(1000)); // 模拟加载时间
            g_hardware_OS = GetOSInfoWMI();
            std::this_thread::sleep_for(std::chrono::milliseconds(500));
            g_hardware_CPU = GetCpuInfoWMI();
            std::this_thread::sleep_for(std::chrono::milliseconds(500));
            g_hardware_MOTHER = GetMotherboardInfo();
            std::this_thread::sleep_for(std::chrono::milliseconds(500));
            g_hardware_GPU = GetGpuInfoWMI();
            std::this_thread::sleep_for(std::chrono::milliseconds(500));
            g_hardware_MEM = GetMemoryInfoWMI();
            
            hardwareInfoComplete.store(true);
            loadingManager.markUIReady();
        }
        catch (const std::exception& e) {
            // 静默处理异常
            hardwareInfoComplete.store(true);
            loadingManager.markUIReady();
        }
    }); // 线程由统一线程管理器管理

    // 检查硬件信息线程是否创建成功
    if (hardwareThreadResult.empty()) {
        // 如果线程创建失败，直接标记为完成
        hardwareInfoComplete.store(true);
        loadingManager.markUIReady();
    }

    // mainloop
    MSG msg;
    bool done = false;
    int currentLoadingStep = 0;
    auto lastStepTime = std::chrono::steady_clock::now();
    
    while (!done)
    {
        //关闭开关
        while (::PeekMessage(&msg, nullptr, 0, 0, PM_REMOVE))
        {
            if (msg.message == WM_QUIT)
                done = true;
            ::TranslateMessage(&msg);
            ::DispatchMessage(&msg);
        }
        if (done)
            break;

        // 开始新帧
        ImGui_ImplDX11_NewFrame();
        ImGui_ImplWin32_NewFrame();
        ImGui::NewFrame();

        // 根据加载状态决定渲染什么
        if (loadingManager.isLoading()) {
            // 控制加载步骤的执行速度
            auto currentTime = std::chrono::steady_clock::now();
            auto timeSinceLastStep = std::chrono::duration_cast<std::chrono::milliseconds>(currentTime - lastStepTime);
            
            // 每500毫秒执行下一个步骤
            if (timeSinceLastStep.count() >= 500 && currentLoadingStep < loadingManager.getStepCount()) {
                loadingManager.executeStep(currentLoadingStep);
                currentLoadingStep++;
                lastStepTime = currentTime;
            }
            
            loadingManager.renderLoadingScreen();
        } else {
            // 调用独立的 UI 渲染函数
            RenderUI();
        }
        
        // 渲染关闭确认对话框
        if (g_showExitConfirmDialog) {
            ImGui::OpenPopup("退出确认");
            ImGui::SetNextWindowPos(ImGui::GetMainViewport()->GetCenter(), ImGuiCond_Appearing, ImVec2(0.5f, 0.5f));
            if (ImGui::BeginPopupModal("退出确认", nullptr, ImGuiWindowFlags_AlwaysAutoResize)) {
                ImGui::Text("确定要退出应用程序吗？");
                ImGui::Text("所有未保存的数据将会丢失。");
                ImGui::Separator();
                
                if (ImGui::Button("确定退出", ImVec2(120, 0))) {
                    g_shouldExit = true;
                    ImGui::CloseCurrentPopup();
                    g_showExitConfirmDialog = false;
                }
                ImGui::SameLine();
                if (ImGui::Button("取消", ImVec2(120, 0))) {
                    ImGui::CloseCurrentPopup();
                    g_showExitConfirmDialog = false;
                }
                ImGui::EndPopup();
            }
        }
        
        // 检查是否应该退出
        if (g_shouldExit) {
            done = true;
        }
 
        // 渲染
        ImGui::Render();
        const float clear_color[4] = { 0.929f, 0.953f, 0.976f, 1.0f }; //颜色为:#EDF3F9
        g_pd3dDeviceContext->OMSetRenderTargets(1, &g_mainRenderTargetView, nullptr);
        g_pd3dDeviceContext->ClearRenderTargetView(g_mainRenderTargetView, clear_color);
        ImGui_ImplDX11_RenderDrawData(ImGui::GetDrawData());

        g_pSwapChain->Present(1, 0);
    }

    // 清理资源
    
    // 关闭线程安全的日志系统，确保所有日志都被正确处理
    LogManager::getInstance()->shutdown();
    
    ImGui_ImplDX11_Shutdown();
    ImGui_ImplWin32_Shutdown();
    ImGui::DestroyContext();

    CleanupDeviceD3D();
    ::DestroyWindow(hwnd);
    ::UnregisterClassW(wc.lpszClassName, wc.hInstance);

    return 0;
}

// 创建Direct3D设备
bool CreateDeviceD3D(HWND hWnd)
{
    DXGI_SWAP_CHAIN_DESC sd;
    ZeroMemory(&sd, sizeof(sd));
    sd.BufferCount = 2;
    sd.BufferDesc.Width = 0;
    sd.BufferDesc.Height = 0;
    sd.BufferDesc.Format = DXGI_FORMAT_R8G8B8A8_UNORM;
    sd.BufferDesc.RefreshRate.Numerator = 60;
    sd.BufferDesc.RefreshRate.Denominator = 1;
    sd.Flags = DXGI_SWAP_CHAIN_FLAG_ALLOW_MODE_SWITCH;
    sd.BufferUsage = DXGI_USAGE_RENDER_TARGET_OUTPUT;
    sd.OutputWindow = hWnd;
    sd.SampleDesc.Count = 1;
    sd.SampleDesc.Quality = 0;
    sd.Windowed = TRUE;
    sd.SwapEffect = DXGI_SWAP_EFFECT_DISCARD;

    UINT createDeviceFlags = 0;
    //createDeviceFlags |= D3D11_CREATE_DEVICE_DEBUG;
    D3D_FEATURE_LEVEL featureLevel;
    const D3D_FEATURE_LEVEL featureLevelArray[2] = { D3D_FEATURE_LEVEL_11_0, D3D_FEATURE_LEVEL_10_0, };
    HRESULT res = D3D11CreateDeviceAndSwapChain(nullptr, D3D_DRIVER_TYPE_HARDWARE, nullptr, createDeviceFlags, featureLevelArray, 2, D3D11_SDK_VERSION, &sd, &g_pSwapChain, &g_pd3dDevice, &featureLevel, &g_pd3dDeviceContext);
    if (res == DXGI_ERROR_UNSUPPORTED) // Try high-performance WARP software driver if hardware is not available.
        res = D3D11CreateDeviceAndSwapChain(nullptr, D3D_DRIVER_TYPE_WARP, nullptr, createDeviceFlags, featureLevelArray, 2, D3D11_SDK_VERSION, &sd, &g_pSwapChain, &g_pd3dDevice, &featureLevel, &g_pd3dDeviceContext);
    if (res != S_OK)
        return false;
    CreateRenderTarget();
    return true;
}

void CleanupDeviceD3D()
{
    CleanupRenderTarget();
    if (g_pSwapChain) { g_pSwapChain->Release(); g_pSwapChain = nullptr; }
    if (g_pd3dDeviceContext) { g_pd3dDeviceContext->Release(); g_pd3dDeviceContext = nullptr; }
    if (g_pd3dDevice) { g_pd3dDevice->Release(); g_pd3dDevice = nullptr; }
}

void CreateRenderTarget()
{
    ID3D11Texture2D* pBackBuffer;
    g_pSwapChain->GetBuffer(0, IID_PPV_ARGS(&pBackBuffer));
    g_pd3dDevice->CreateRenderTargetView(pBackBuffer, nullptr, &g_mainRenderTargetView);
    pBackBuffer->Release();
}

void CleanupRenderTarget()
{
    if (g_mainRenderTargetView) { g_mainRenderTargetView->Release(); g_mainRenderTargetView = nullptr; }
}

// Forward declare message handler from imgui_impl_win32.cpp
extern IMGUI_IMPL_API LRESULT ImGui_ImplWin32_WndProcHandler(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam);
LRESULT WINAPI WndProc(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam)
{
    if (ImGui_ImplWin32_WndProcHandler(hWnd, msg, wParam, lParam))
        return true;

    switch (msg)
    {
    case WM_GETMINMAXINFO:
        {
            MINMAXINFO* pMinMax = (MINMAXINFO*)lParam;
            pMinMax->ptMinTrackSize.x = 800;  // 最小宽度
            pMinMax->ptMinTrackSize.y = 600;  // 最小高度
            pMinMax->ptMaxTrackSize.x = GetSystemMetrics(SM_CXSCREEN); // 屏幕宽度
            pMinMax->ptMaxTrackSize.y = GetSystemMetrics(SM_CYSCREEN); // 屏幕高度
            return 0;
        }
    case WM_SIZE:
        if (g_pd3dDevice != nullptr && wParam != SIZE_MINIMIZED)
        {
            CleanupRenderTarget();
            g_pSwapChain->ResizeBuffers(0, (UINT)LOWORD(lParam), (UINT)HIWORD(lParam), DXGI_FORMAT_UNKNOWN, 0);
            CreateRenderTarget();
        }
        return 0;
    case WM_CLOSE:
        // 显示关闭确认对话框，而不是直接关闭
        g_showExitConfirmDialog = true;
        return 0;
    case WM_SYSCOMMAND:
        if ((wParam & 0xfff0) == SC_KEYMENU)
            return 0;
        if ((wParam & 0xfff0) == SC_CLOSE) {
            // 处理Alt+F4等系统关闭命令
            g_showExitConfirmDialog = true;
            return 0;
        }
        break;
    case WM_DESTROY:
        ::PostQuitMessage(0);
        return 0;
    }

    return ::DefWindowProcW(hWnd, msg, wParam, lParam);
}
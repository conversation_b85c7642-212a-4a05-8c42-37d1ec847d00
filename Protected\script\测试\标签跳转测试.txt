// 标签跳转功能测试脚本
// 测试LABEL_LOCATE和LABEL:命令的正确实现

slice(1) {
    // 测试1：直接跳转到指定标签
    LABEL_LOCATE(燃犀之灯)
    
    // 这行不应该执行，因为会跳转到燃犀之灯标签
    MOUSE_MOVE(100, 100)
    DELAY(1000)
    
    LABEL:燃犀之灯
    TEXT_MATCH(燃犀之灯, left, 1050, 400, 230, 200)
    DELAY(500)
    MOUSE_LEFT()
    
    // 测试2：跳转到另一个标签
    LABEL_LOCATE(猫儿偷鸡)
    
    // 这行也不应该执行
    MOUSE_MOVE(200, 200)
    
    LABEL:猫儿偷鸡
    TEXT_MATCH(猫儿偷鸡, left, 1050, 400, 230, 200)
    DELAY(1000)
    MOUSE_LEFT()
    
    // 测试3：跳转到不存在的标签（应该暂停切片）
    LABEL_LOCATE(不存在的标签)
    
    // 如果标签不存在，这行应该不会执行，因为切片会被暂停
    MOUSE_MOVE(300, 300)
}

slice(2) {
    // 测试切片2中的标签跳转
    DELAY(2000)
    LABEL_LOCATE(切片2标签)
    
    MOUSE_MOVE(400, 400)
    
    LABEL:切片2标签
    TEXT_MATCH(测试文本, left, 500, 500, 100, 50)
    MOUSE_LEFT()
}

#ifndef VMTASKS_H
#define VMTASKS_H

#include <string>
#include <vector>
#include <future>
#include <memory>
#include <variant>
#include <opencv2/opencv.hpp> // For cv::Point
#include <rfb/rfbclient.h> // Include the necessary VNC client header
#include "MouseBoardCode.h" // Assuming InputTask is defined here and might use rfbClient
#include "VNCControl.h" // Include VNCControl.h for handleInput function

// Forward declaration for VMVision to avoid circular dependency
class VMVision;

// Forward declaration
class ScreenFrameDistributor;

// --- Result Structures ---

struct BaseTaskResult {
    bool success = false;
    std::string errorMessage;
    // Potentially a unique ID for the task instance if needed for correlation
    // int taskId = 0;
};

struct KeyboardMouseResult : public BaseTaskResult {};

struct VisionMatchResult : public BaseTaskResult {
    cv::Point matchLocation; // Only valid if success is true
    double confidence = 0.0; // Confidence score of the match
};

struct WordsMatchResult : public BaseTaskResult {
    cv::Point matchLocation; // Only valid if success is true (first word/phrase found)
    std::vector<cv::Rect> allMatchLocations; // Bounding boxes for all occurrences
    std::vector<std::string> recognizedTexts; // Recognized texts corresponding to allMatchLocations
    // double overallConfidence = 0.0;
};

// 文本识别结果结构
struct TextRecognizeResult : public BaseTaskResult {
    std::vector<std::string> recognizedTexts; // 识别到的所有文本
    std::vector<cv::Rect> textLocations; // 每个文本的位置
    std::vector<double> confidences; // 每个文本的置信度
};

// 等待画面静止结果结构
struct WaitForScreenStillResult : public BaseTaskResult {
    bool isStill = false; // 画面是否已静止
    int totalChecks = 0; // 总检测次数
    double elapsedSeconds = 0.0; // 实际等待时间（秒）
};

// 等待视觉匹配结果结构
struct WaitForVisualMatchResult : public BaseTaskResult {
    bool matchFound = false; // 是否找到匹配
    cv::Point matchLocation; // 匹配位置（仅在success和matchFound为true时有效）
    double confidence = 0.0; // 匹配置信度
    int totalChecks = 0; // 总检测次数
    double elapsedSeconds = 0.0; // 实际等待时间（秒）
};

// 标签定位结果结构
struct LabelLocateResult : public BaseTaskResult {
    bool labelFound = false; // 是否找到标签
    std::string foundLabelName; // 找到的标签名称
    int jumpToLine = -1; // 跳转到的行号
    std::string recognizedText; // OCR识别到的文本（仅在使用ROI时有值）
};

// --- Task Enum and Base Task ---

enum class VMTaskType {
    NONE,
    TEMPLATE_MATCH,
    LABEL_LOCATE,
    CLICK,
    MOVE,
    KEY_PRESS,
    DELAY,
    LABEL,  // 标签定义任务类型
    KEYBOARD_MOUSE_ACTION,              // 直接键盘鼠标操作
    IMAGE_MATCH_VISION,
    WORDS_MATCH_VISION,
    TEXT_RECOGNIZE_VISION,
    WAIT_FOR_SCREEN_STILL,
    WAIT_FOR_VISUAL_MATCH,  // 等待视觉匹配类型

};

class IVMTask {
public:
    virtual ~IVMTask() = default;
    virtual VMTaskType getType() const = 0;
    // The 'execute' method will be the core logic for each task type.
    // For KeyboardMouseActionTask, it uses rfbClient.
    // For Vision tasks, DispatchCenter::processXYZTask will likely call VMVision methods and set the promise directly.
    // So, the execute signature here primarily serves KeyboardMouseActionTask or acts as a generic interface point.
    virtual void execute(rfbClient* vncClient, VMVision* visionProcessor /* = nullptr */) = 0;

    // 新增：清除promise以避免重复设置（用于任务重新入队）
    // virtual void clearPromise() = 0;

    // 新增：获取切片号（所有任务都需要这个方法）
    virtual int getSliceNumber() const = 0;

    const std::string& getVmName() const { return vmName_; } // Getter for vmName

protected:
    // Constructor for base class to initialize vmName
    IVMTask(std::string vmName) : vmName_(std::move(vmName)) {}
    // IVMTask() = default; // Allow default constructor for potential direct use of promise_

    std::string vmName_; // Added vmName member
};

// --- Concrete Task Implementations ---

// 1. 直接键盘鼠标操作任务
class DirectKeyboardMouseActionTask : public IVMTask {
public:
    DirectKeyboardMouseActionTask(std::string vmName, 
                                 std::vector<InputTask> actions, 
                                 std::shared_ptr<std::promise<KeyboardMouseResult>> promise,
                                 int sliceNumber = 1)
        : IVMTask(std::move(vmName)), actions_(std::move(actions)), promise_(std::move(promise)),
          sliceNumber_(sliceNumber) {}

    VMTaskType getType() const override { return VMTaskType::KEYBOARD_MOUSE_ACTION; }

    void execute(rfbClient* vncClient, VMVision* visionProcessor /*unused*/) override;
    
    std::shared_ptr<std::promise<KeyboardMouseResult>> getPromise() { return promise_; }
    
    // 坐标转换支持的访问器方法
    int getSliceNumber() const { return sliceNumber_; }
    const std::vector<InputTask>& getActions() const { return actions_; }
    void setActions(const std::vector<InputTask>& actions) { actions_ = actions; }

    // 实现clearPromise方法
    /*
    void clearPromise() override {
        promise_ = std::make_shared<std::promise<KeyboardMouseResult>>();
    }
    */

private:
    std::vector<InputTask> actions_;
    std::shared_ptr<std::promise<KeyboardMouseResult>> promise_;
    int sliceNumber_;
};

// 2. Image Match Vision Task
class ImageMatchVisionTask : public IVMTask {
public:
    ImageMatchVisionTask(std::string vmName, 
                         std::string templateName, 
                         double threshold, 
                         std::string imagePath, // Added based on DispatchCenter usage
                         cv::Rect roi,         // Added based on DispatchCenter usage
                         int sliceNumber,
                         std::string mouseClickType, // 新增：鼠标点击类型 "left", "right", "double"
                         std::shared_ptr<std::promise<VisionMatchResult>> promise
                         )
        : IVMTask(std::move(vmName)), templateName_(std::move(templateName)), 
          threshold_(threshold), imagePath_(std::move(imagePath)),
          roi_(roi), sliceNumber_(sliceNumber), mouseClickType_(std::move(mouseClickType)), 
          promise_(promise ? std::move(promise) : std::make_shared<std::promise<VisionMatchResult>>()) {}

    // 拷贝构造函数 - 创建新的promise以避免共享
    ImageMatchVisionTask(const ImageMatchVisionTask& other)
        : IVMTask(other.getVmName()), templateName_(other.templateName_), 
          threshold_(other.threshold_), imagePath_(other.imagePath_), roi_(other.roi_), 
          sliceNumber_(other.sliceNumber_), mouseClickType_(other.mouseClickType_), 
          promise_(std::make_shared<std::promise<VisionMatchResult>>()) {} // 创建新的promise

    VMTaskType getType() const override { return VMTaskType::IMAGE_MATCH_VISION; }

    // execute() might be minimal if DispatchCenter::processImageMatchTask handles logic.
    // It's given VMVision* in case it needs to interact with it directly.
    void execute(rfbClient* vncClient /*unused*/, VMVision* visionProcessor) override { 
        // If DispatchCenter::processImageMatchTask handles the logic and promise setting,
        // this execute method might do nothing or log an error if called unexpectedly.
        // Or, it could be the designated place to call visionProcessor methods.
        // For now, let's assume DispatchCenter's process method will set the promise.
        if (!visionProcessor && promise_) {
            VisionMatchResult res; 
            res.success = false; 
            res.errorMessage = "VMVision processor not provided to ImageMatchVisionTask::execute.";
            promise_->set_value(res);
        }
        // Actual vision processing logic and promise setting should happen here or in DispatchCenter.
        // If here, it would use visionProcessor->matchTemplateAsync(...) or similar.
    }

    // Getters for parameters needed by DispatchCenter::processImageMatchTask
    const std::string& getTemplateName() const { return templateName_; }
    double getThreshold() const { return threshold_; }
    const std::string& getImagePath() const { return imagePath_; }
    cv::Rect getRoi() const { return roi_; }
    int getSliceNumber() const { return sliceNumber_; }
    const std::string& getMouseClickType() const { return mouseClickType_; } // 新增getter
    std::shared_ptr<std::promise<VisionMatchResult>> getPromise() { return promise_; }

    // 实现clearPromise方法
    /*
    void clearPromise() override {
        promise_ = std::make_shared<std::promise<VisionMatchResult>>();
    }
    */

private:
    // VMVision* visionProcessor_; // Removed, will be passed to execute or used by DispatchCenter
    std::string templateName_;
    double threshold_;
    std::string imagePath_;
    cv::Rect roi_;
    int sliceNumber_;
    std::string mouseClickType_; // 新增：鼠标点击类型
    std::shared_ptr<std::promise<VisionMatchResult>> promise_;
};

// 4. Words Match Vision Task
class WordsMatchVisionTask : public IVMTask {
public:
    WordsMatchVisionTask(std::string vmName, 
                         std::vector<std::string> wordsToMatch, 
                         double threshold, 
                         cv::Rect roi, 
                         int sliceNumber, 
                         std::string mouseClickType, // 新增：鼠标点击类型 "left", "right", "double"
                         std::shared_ptr<std::promise<WordsMatchResult>> promise)
        : IVMTask(std::move(vmName)), wordsToMatch_(std::move(wordsToMatch)), 
          threshold_(threshold), roi_(roi), sliceNumber_(sliceNumber), mouseClickType_(std::move(mouseClickType)), 
          promise_(promise ? std::move(promise) : std::make_shared<std::promise<WordsMatchResult>>()) {}

    // 拷贝构造函数 - 创建新的promise以避免共享
    WordsMatchVisionTask(const WordsMatchVisionTask& other)
        : IVMTask(other.getVmName()), wordsToMatch_(other.wordsToMatch_), 
          threshold_(other.threshold_), roi_(other.roi_), sliceNumber_(other.sliceNumber_), 
          mouseClickType_(other.mouseClickType_), 
          promise_(std::make_shared<std::promise<WordsMatchResult>>()) {} // 创建新的promise

    VMTaskType getType() const override { return VMTaskType::WORDS_MATCH_VISION; }

    void execute(rfbClient* vncClient /*unused*/, VMVision* visionProcessor) override {
        // Similar to ImageMatchVisionTask, logic likely in DispatchCenter::processWordsMatchTask
        if (!visionProcessor && promise_) {
            WordsMatchResult res; 
            res.success = false; 
            res.errorMessage = "VMVision processor not provided to WordsMatchVisionTask::execute.";
            promise_->set_value(res);
        }
    }
    
    // Getters for parameters
    const std::vector<std::string>& getWordsToMatch() const { return wordsToMatch_; }
    double getThreshold() const { return threshold_; }
    cv::Rect getRoi() const { return roi_; }
    int getSliceNumber() const { return sliceNumber_; }
    const std::string& getMouseClickType() const { return mouseClickType_; } // 新增getter
    std::shared_ptr<std::promise<WordsMatchResult>> getPromise() { return promise_; }

    // 实现clearPromise方法
    /*
    void clearPromise() override {
        promise_ = std::make_shared<std::promise<WordsMatchResult>>();
    }
    */

private:
    // VMVision* visionProcessor_; // Removed
    std::vector<std::string> wordsToMatch_;
    double threshold_;
    cv::Rect roi_;
    int sliceNumber_;
    std::string mouseClickType_; // 新增：鼠标点击类型
    std::shared_ptr<std::promise<WordsMatchResult>> promise_;
};

// 5. Text Recognize Vision Task
class TextRecognizeVisionTask : public IVMTask {
public:
    TextRecognizeVisionTask(std::string vmName, 
                           cv::Rect roi, 
                           int sliceNumber, 
                           std::shared_ptr<std::promise<TextRecognizeResult>> promise)
        : IVMTask(std::move(vmName)), roi_(roi), sliceNumber_(sliceNumber), promise_(std::move(promise)) {}

    VMTaskType getType() const override { return VMTaskType::TEXT_RECOGNIZE_VISION; }

    void execute(rfbClient* vncClient /*unused*/, VMVision* visionProcessor) override {
        // Similar to other vision tasks, logic likely in DispatchCenter::processTextRecognizeTask
        if (!visionProcessor && promise_) {
            TextRecognizeResult res; 
            res.success = false; 
            res.errorMessage = "VMVision processor not provided to TextRecognizeVisionTask::execute.";
            promise_->set_value(res);
        }
    }
    
    // Getters for parameters
    cv::Rect getRoi() const { return roi_; }
    int getSliceNumber() const { return sliceNumber_; }
    std::shared_ptr<std::promise<TextRecognizeResult>> getPromise() { return promise_; }

    // 实现clearPromise方法
    /*
    void clearPromise() override {
        promise_ = std::make_shared<std::promise<TextRecognizeResult>>();
    }
    */

private:
    cv::Rect roi_;
    int sliceNumber_;
    std::shared_ptr<std::promise<TextRecognizeResult>> promise_;
};

// 6. Wait For Screen Still Task
class WaitForScreenStillTask : public IVMTask {
public:
    WaitForScreenStillTask(std::string vmName,
                          int sliceNumber,
                          cv::Rect roi,
                          int checkIntervalSeconds,
                          int maxTimeoutSeconds,
                          std::shared_ptr<std::promise<WaitForScreenStillResult>> promise)
        : IVMTask(std::move(vmName)), sliceNumber_(sliceNumber), roi_(roi),
          checkIntervalSeconds_(checkIntervalSeconds), maxTimeoutSeconds_(maxTimeoutSeconds),
          promise_(std::move(promise)) {}

    VMTaskType getType() const override { return VMTaskType::WAIT_FOR_SCREEN_STILL; }

    void execute(rfbClient* vncClient /*unused*/, VMVision* visionProcessor) override {
        // Similar to other vision tasks, logic likely in DispatchCenter::processWaitForScreenStillTask
        if (!visionProcessor && promise_) {
            WaitForScreenStillResult res;
            res.success = false;
            res.errorMessage = "VMVision processor not provided to WaitForScreenStillTask::execute.";
            promise_->set_value(res);
        }
    }

    // Getters for parameters
    int getSliceNumber() const { return sliceNumber_; }
    cv::Rect getRoi() const { return roi_; }
    int getCheckIntervalSeconds() const { return checkIntervalSeconds_; }
    int getMaxTimeoutSeconds() const { return maxTimeoutSeconds_; }
    std::shared_ptr<std::promise<WaitForScreenStillResult>> getPromise() { return promise_; }

    // 实现clearPromise方法
    /*
    void clearPromise() override {
        promise_ = std::make_shared<std::promise<WaitForScreenStillResult>>();
    }
    */

private:
    int sliceNumber_;
    cv::Rect roi_;
    int checkIntervalSeconds_;
    int maxTimeoutSeconds_;
    std::shared_ptr<std::promise<WaitForScreenStillResult>> promise_;
};

// 7. Wait For Visual Match Task
class WaitForVisualMatchTask : public IVMTask {
public:
    WaitForVisualMatchTask(std::string vmName,
                          int sliceNumber,
                          std::string templateName,
                          int checkIntervalSeconds,
                          int maxTimeoutSeconds,
                          double threshold,
                          std::shared_ptr<std::promise<WaitForVisualMatchResult>> promise)
        : IVMTask(std::move(vmName)), sliceNumber_(sliceNumber), templateName_(std::move(templateName)),
          checkIntervalSeconds_(checkIntervalSeconds), maxTimeoutSeconds_(maxTimeoutSeconds),
          threshold_(threshold), promise_(std::move(promise)) {}

    VMTaskType getType() const override { return VMTaskType::WAIT_FOR_VISUAL_MATCH; }

    void execute(rfbClient* vncClient /*unused*/, VMVision* visionProcessor) override {
        // Similar to other vision tasks, logic likely in DispatchCenter::processWaitForVisualMatchTask
        if (!visionProcessor && promise_) {
            WaitForVisualMatchResult res;
            res.success = false;
            res.errorMessage = "VMVision processor not provided to WaitForVisualMatchTask::execute.";
            promise_->set_value(res);
        }
    }

    // Getters for parameters
    int getSliceNumber() const { return sliceNumber_; }
    std::string getTemplateName() const { return templateName_; }
    int getCheckIntervalSeconds() const { return checkIntervalSeconds_; }
    int getMaxTimeoutSeconds() const { return maxTimeoutSeconds_; }
    double getThreshold() const { return threshold_; }
    std::shared_ptr<std::promise<WaitForVisualMatchResult>> getPromise() { return promise_; }

    // 实现clearPromise方法
    /*
    void clearPromise() override {
        promise_ = std::make_shared<std::promise<WaitForVisualMatchResult>>();
    }
    */

private:
    int sliceNumber_;
    std::string templateName_;
    int checkIntervalSeconds_;
    int maxTimeoutSeconds_;
    double threshold_;
    std::shared_ptr<std::promise<WaitForVisualMatchResult>> promise_;
};

// 8. Label Locate Task - 标签定位任务（仅支持文本模式）
class LabelLocateTask : public IVMTask {
public:
    // 构造函数：用于文本模式标签定位
    LabelLocateTask(std::string vmName,
                    std::string targetText,
                    int sliceNumber)
        : IVMTask(std::move(vmName)),
          targetText_(std::move(targetText)),
          sliceNumber_(sliceNumber) {}

    VMTaskType getType() const override { return VMTaskType::LABEL_LOCATE; }

    void execute(rfbClient* vncClient /*unused*/, VMVision* visionProcessor) override {
        // 标签定位任务的执行逻辑在DispatchCenter中实现
        // 这里不需要执行任何操作，只是标记任务类型
    }

    // Getters for parameters
    const std::string& getTargetText() const { return targetText_; }
    int getSliceNumber() const { return sliceNumber_; }

private:
    std::string targetText_;         // 目标标签文本
    int sliceNumber_;                // 切片号
};

// 9. Label Task - 标签定义任务
class LabelTask : public IVMTask {
public:
    // 构造函数：用于标签定义
    LabelTask(std::string vmName,
              std::string labelName,
              int sliceNumber)
        : IVMTask(std::move(vmName)),
          labelName_(std::move(labelName)),
          sliceNumber_(sliceNumber) {}

    VMTaskType getType() const override { return VMTaskType::LABEL; }

    void execute(rfbClient* vncClient /*unused*/, VMVision* visionProcessor) override {
        // 标签定义任务不需要执行任何操作，只是标记位置
        // 实际的标签位置记录在SliceContainer中完成
    }

    // Getters for parameters
    const std::string& getLabelName() const { return labelName_; }
    int getSliceNumber() const { return sliceNumber_; }

private:
    std::string labelName_;          // 标签名称
    int sliceNumber_;                // 切片号
};



#endif // VMTASKS_H
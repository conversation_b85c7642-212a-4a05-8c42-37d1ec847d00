#include "SimpleOCR.h"
#include "ocr_det.h"
#include "ocr_rec.h"
#include "utility.h"
#include <algorithm>
#include <iostream>
#include <iomanip>
#include <memory>
#include <stdexcept>
#include <string>

namespace PaddleOCR {

// OCR参数配置结构体
struct OCRParams {
    // 检测参数
    float det_db_thresh;        // 文本检测二值化阈值
    float det_db_box_thresh;    // 文本检测框阈值
    float det_db_unclip_ratio;  // 文本检测框扩大系数
    int limit_side_len;         // 图像尺寸限制
    std::string det_db_score_mode; // 检测分数模式
    
    // 识别参数
    int rec_batch_num;          // 识别批次大小
    int rec_img_h;              // 识别图像高度
    int rec_img_w;              // 识别图像宽度
    
    // 其他参数
    bool use_dilation;          // 是否使用膨胀
    bool enable_mkldnn;         // 是否启用MKLDNN加速
    
    std::string name;           // 配置名称
};

// 定义3种不同的参数配置
static const OCRParams kParamConfigs[] = {
    // 配置0: 高精度配置 - 降低阈值，更容易检测到文本
    {
        0.25f,    // det_db_thresh
        0.4f,     // det_db_box_thresh
        1.8f,     // det_db_unclip_ratio
        1024,     // limit_side_len
        "slow",   // det_db_score_mode
        1,        // rec_batch_num
        48,       // rec_img_h
        320,      // rec_img_w
        true,     // use_dilation
        true,     // enable_mkldnn
        "高精度配置" // name
    },
    
    // 配置1: 高召回率配置 - 更容易检测到更多文本，但可能有更多误检
    {
        0.2f,     // det_db_thresh
        0.3f,     // det_db_box_thresh
        2.0f,     // det_db_unclip_ratio
        1200,     // limit_side_len
        "fast",   // det_db_score_mode
        1,        // rec_batch_num
        32,       // rec_img_h
        320,      // rec_img_w
        true,     // use_dilation
        false,    // enable_mkldnn
        "高召回率配置" // name
    },
    
    // 配置2: 混合配置 - 结合高精度和高召回率的优点
    {
        0.225f,   // det_db_thresh - 介于高精度和高召回率之间
        0.35f,    // det_db_box_thresh - 介于高精度和高召回率之间
        1.9f,     // det_db_unclip_ratio - 介于高精度和高召回率之间
        1100,     // limit_side_len - 介于高精度和高召回率之间
        "slow",   // det_db_score_mode - 使用高精度配置的值
        1,        // rec_batch_num
        40,       // rec_img_h - 介于高精度和高召回率之间
        320,      // rec_img_w - 使用两者相同的值
        true,     // use_dilation - 两者相同
        true,     // enable_mkldnn - 使用高精度配置的值
        "高精度-高召回率混合配置" // name
    }
};

// Private implementation class (PIMPL pattern)
class SimpleOCR::Impl {
public:
    Impl(const std::string& det_model_dir, 
         const std::string& rec_model_dir,
         const std::string& dict_path,
         bool use_gpu, 
         int gpu_id,
         SimpleOCR::ParamConfigIndex config_index) {
        
        try {
            // 保存模型路径和GPU设置
            this->det_model_dir = det_model_dir;
            this->rec_model_dir = rec_model_dir;
            this->dict_path = dict_path;
            this->use_gpu = use_gpu;
            this->gpu_id = gpu_id;
            
            // 设置参数配置
            setParamConfig(config_index);
            
            // SimpleOCR初始化成功
            
        } catch (const std::exception& e) {
            // SimpleOCR初始化错误
            throw;
        }
    }
    
    // 设置参数配置并重新初始化检测器和识别器
    void setParamConfig(SimpleOCR::ParamConfigIndex config_index) {
        if (config_index < 0 || config_index >= SimpleOCR::CONFIG_COUNT) {
            // 无效的配置索引，使用默认配置
            config_index = SimpleOCR::CONFIG_HIGH_PRECISION;
        }
        
        current_config_index = config_index;
        const OCRParams& params = kParamConfigs[config_index];
        
        // 切换到参数配置
        
        // 初始化检测器
        detector = std::make_unique<DBDetector>(
            det_model_dir,
            use_gpu,
            gpu_id,
            500, // gpu_mem
            10,  // cpu_threads
            params.enable_mkldnn, // enable_mkldnn
            "max", // limit_type
            params.limit_side_len, // limit_side_len
            params.det_db_thresh,  // det_db_thresh
            params.det_db_box_thresh,  // det_db_box_thresh
            params.det_db_unclip_ratio,  // det_db_unclip_ratio
            params.det_db_score_mode, // det_db_score_mode
            params.use_dilation,  // use_dilation
            false,  // use_tensorrt
            "fp32"  // precision
        );
        
        // 初始化识别器
        recognizer = std::make_unique<CRNNRecognizer>(
            rec_model_dir,
            use_gpu,
            gpu_id,
            500, // gpu_mem
            10,  // cpu_threads
            params.enable_mkldnn, // enable_mkldnn
            dict_path,
            false, // use_tensorrt
            "fp32", // precision
            params.rec_batch_num, // rec_batch_num
            params.rec_img_h,     // rec_img_h
            params.rec_img_w      // rec_img_w
        );
    }
    
    // 获取当前配置名称
    std::string getCurrentConfigName() const {
        if (current_config_index >= 0 && current_config_index < SimpleOCR::CONFIG_COUNT) {
            return kParamConfigs[current_config_index].name;
        }
        return "未知配置";
    }
    
    // Run OCR detection on an image
    std::vector<OCRPredictResult> runOCR(const cv::Mat& image) {
        std::vector<OCRPredictResult> results;
        
        try {
            // Step 1: Run text detection
            std::vector<std::vector<std::vector<int>>> boxes;
            std::vector<double> det_times;
            detector->Run(image, boxes, det_times);
            
            // Step 2: Prepare detected text regions for recognition
            std::vector<cv::Mat> img_list;
            std::vector<OCRPredictResult> temp_results;
            
            for (size_t j = 0; j < boxes.size(); j++) {
                const auto& box = boxes[j];
                
                OCRPredictResult result;
                result.box = box; // 保存原始坐标
                // 清除cls_label字段，避免存储无效数据
                result.cls_label = 0;
                result.text = "";  // 初始化文本字段
                result.score = 0.0f;  // 初始化分数字段
                temp_results.push_back(result);
                
                // Crop the detected region
                cv::Mat crop_img = Utility::GetRotateCropImage(image, box);
                if (!crop_img.empty()) {
                    img_list.push_back(crop_img);
                } else {
                    // 如果裁剪失败，添加一个空图像占位
                    img_list.push_back(cv::Mat::zeros(32, 32, CV_8UC3));
                }
            }
            
            // 确保temp_results和img_list的大小一致
            if (temp_results.size() != img_list.size()) {
                // 检测结果和裁剪图像不匹配
                return results;
            }
            
            // Sort boxes from top to bottom, left to right
            // 创建索引映射以跟踪排序前后的对应关系
            std::vector<size_t> sorted_indices(temp_results.size());
            for (size_t i = 0; i < sorted_indices.size(); i++) {
                sorted_indices[i] = i;
            }
            
            // 使用Utility::sort_boxes排序，但保持索引映射
            std::vector<OCRPredictResult> sorted_results = temp_results;
            Utility::sort_boxes(sorted_results);
            
            // 重新建立索引映射：找到排序后每个元素在原始数组中的位置
            for (size_t i = 0; i < sorted_results.size(); i++) {
                for (size_t j = 0; j < temp_results.size(); j++) {
                    // 通过比较边界框坐标来找到对应关系
                    if (sorted_results[i].box == temp_results[j].box) {
                        sorted_indices[i] = j;
                        break;
                    }
                }
            }
            
            // Step 3: Run text recognition on cropped regions
            if (!img_list.empty() && !sorted_results.empty()) {
                std::vector<std::string> rec_texts(sorted_results.size());
                std::vector<float> rec_scores(sorted_results.size());
                std::vector<double> rec_times;
                
                // 创建与排序后顺序相匹配的图像列表
                std::vector<cv::Mat> sorted_img_list;
                sorted_img_list.reserve(sorted_results.size());
                
                for (size_t i = 0; i < sorted_results.size(); i++) {
                    size_t original_idx = sorted_indices[i];
                    // 添加边界检查
                    if (original_idx < img_list.size()) {
                        sorted_img_list.push_back(img_list[original_idx]);
                    } else {
                        // 索引越界警告
                        // 添加一个空图像作为占位符
                        sorted_img_list.push_back(cv::Mat::zeros(32, 32, CV_8UC3));
                    }
                }
                
                // 确保图像列表大小正确
                if (sorted_img_list.size() != sorted_results.size()) {
                    // 排序图像列表大小不匹配
                    return results;
                }
                
                // 使用排序后的图像列表进行识别
                recognizer->Run(sorted_img_list, rec_texts, rec_scores, rec_times);
                
                // Combine results - 确保文本和坐标正确对应
                for (size_t i = 0; i < sorted_results.size() && i < rec_texts.size(); i++) {
                    sorted_results[i].text = rec_texts[i];
                    sorted_results[i].score = rec_scores[i];
                    // 清除可能的垃圾数据
                    sorted_results[i].cls_label = 0;
                    results.push_back(sorted_results[i]);
                }
            }
            
        } catch (const std::exception& e) {
            // OCR处理过程中出错
            // 清理可能的部分结果
            results.clear();
        } catch (...) {
            // OCR处理过程中出现未知错误
            results.clear();
        }
        
        return results;
    }
    
    std::unique_ptr<DBDetector> detector;
    std::unique_ptr<CRNNRecognizer> recognizer;
    
    // 存储模型路径和设置
    std::string det_model_dir;
    std::string rec_model_dir;
    std::string dict_path;
    bool use_gpu;
    int gpu_id;
    
    // 当前参数配置索引
    SimpleOCR::ParamConfigIndex current_config_index = SimpleOCR::CONFIG_HIGH_PRECISION;
};

// SimpleOCR implementation

SimpleOCR::SimpleOCR(
    const std::string& det_model_dir,
    const std::string& rec_model_dir,
    const std::string& dict_path,
    bool use_gpu,
    int gpu_id,
    ParamConfigIndex config_index
) : pImpl(std::make_unique<Impl>(det_model_dir, rec_model_dir, dict_path, use_gpu, gpu_id, config_index)) {}

SimpleOCR::~SimpleOCR() = default;

// 设置参数配置
void SimpleOCR::setParamConfig(ParamConfigIndex config_index) {
    pImpl->setParamConfig(config_index);
}

// 获取当前参数配置名称
std::string SimpleOCR::getCurrentConfigName() const {
    return pImpl->getCurrentConfigName();
}

std::vector<OCRPredictResult> SimpleOCR::recognize(const cv::Mat& image) {
    if (image.empty()) {
        // 输入图像为空
        return {};
    }
    
    // 添加额外的安全检查
    if (!pImpl) {
        // SimpleOCR未正确初始化
        return {};
    }
    
    try {
        return pImpl->runOCR(image);
    } catch (const std::exception& e) {
        // SimpleOCR::recognize中的异常
        return {};
    } catch (...) {
        // SimpleOCR::recognize中的未知异常
        return {};
    }
}

std::pair<cv::Point, float> SimpleOCR::findText(const cv::Mat& image, const std::string& text) {
    if (image.empty() || text.empty()) {
        // 输入图像或文本为空
        return {cv::Point(0, 0), 0.0f};
    }
    
    // 获取当前配置信息
    std::string config_name = pImpl->getCurrentConfigName();
    
    // 运行OCR
    std::vector<OCRPredictResult> results = pImpl->runOCR(image);
    
    // 查找最佳匹配文本
    float best_confidence = 0.0f;
    cv::Point best_center(0, 0);
    OCRPredictResult* best_result = nullptr;
    

    // 计算每个结果与目标文本的相似度
    std::vector<std::pair<float, size_t>> similarity_scores;
    
    for (size_t i = 0; i < results.size(); i++) {
        auto& result = results[i];
        float similarity = 0.0f;
        std::string match_type = "无匹配";
        
        // 计算中心点
        cv::Point center = GetCenterPoint(result);
        
        // 检查精确匹配
        if (result.text == text) {
            similarity = result.score * 2.0f; // 精确匹配给予更高的权重
            match_type = "精确匹配";
        }
        // 检查部分匹配
        else if (result.text.find(text) != std::string::npos) {
            // 计算匹配文本占整个文本的比例
            float match_ratio = static_cast<float>(text.length()) / result.text.length();
            similarity = result.score * match_ratio * 1.5f; // 部分匹配给予较高的权重
            match_type = "部分匹配";
        }
        // 检查模糊匹配（允许小写字母差异）
        else {
            std::string lower_result = result.text;
            std::string lower_text = text;
            std::transform(lower_result.begin(), lower_result.end(), lower_result.begin(), ::tolower);
            std::transform(lower_text.begin(), lower_text.end(), lower_text.begin(), ::tolower);
            
            if (lower_result == lower_text) {
                similarity = result.score * 1.8f; // 大小写不敏感匹配
                match_type = "大小写匹配";
            }
            else if (lower_result.find(lower_text) != std::string::npos) {
                float match_ratio = static_cast<float>(text.length()) / result.text.length();
                similarity = result.score * match_ratio * 1.3f;
                match_type = "模糊匹配";
            }
        }
        
        // 如果有匹配，记录相似度分数
        if (similarity > 0) {
            similarity_scores.push_back({similarity, i});
        }
        
        // 处理识别结果（静默）
    }
    
    // 按相似度分数降序排序
    std::sort(similarity_scores.begin(), similarity_scores.end(), 
              [](const auto& a, const auto& b) { return a.first > b.first; });
    
    // 如果有匹配结果，选择最佳匹配
    if (!similarity_scores.empty()) {
        size_t best_idx = similarity_scores[0].second;
        best_result = &results[best_idx];
        best_confidence = similarity_scores[0].first;
        best_center = GetCenterPoint(*best_result);
        
        // 找到最佳匹配结果
    } else {
        // 未找到匹配文本
    }
    
    // OCR识别结束
    
    return {best_center, best_confidence};
}

} // namespace PaddleOCR

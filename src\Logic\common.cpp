#include <windows.h>
#include <string>
#include <stdexcept>
#include <vector> 
#include <future> 
#include <Vnc/MouseBoardCode.h>
#include <GUI/consolelog/consolelog.h>
#include <Vnc/VMVision.h>// For cv::Rect if not transitively included
#include <Vnc/DispatchCenter.h>
#include <Vnc/VMTasks.h>  // For LabelLocateResult
#include <opencv2/core/types.hpp> // Explicitly include for cv::Rect
#include "Logic/common.h"
#include <thread>               // For std::this_thread::sleep_for
#include <chrono>
#include <GUI/tasklist/tasklist.h>
#include <filesystem>
#include <regex>          // 正则表达式支持
#include <sstream>        // 字符串流处理
#include <Core/ConfigManager.h> // 配置管理器
#include <Core/UnifiedThreadManager.h> // 统一线程管理器
#include <Vnc/VMStateManager.h> // VM状态管理器
#include <type_traits>    // For std::is_same_v and if constexpr support
#include <random>         // 随机数生成支持
#include <fstream>        // 文件流支持
#include <algorithm>      // 算法支持
#include <map>            // Map支持
#include <cstdlib>        // For rand() and srand()
#include <ctime>          // For time()

// 线程本地存储的虚拟机名称
thread_local std::string threadLocalVmName;

// 获取统一线程管理器引用
static UnifiedThreadManager& getThreadManager() {
    return UnifiedThreadManager::getInstance();
}

// 设置线程本地存储的虚拟机名称
void SetThreadLocalVmName(const std::string& vmName) {
    threadLocalVmName = vmName;
}

// 获取线程本地存储的虚拟机名称
std::string GetThreadLocalVmName() {
    return threadLocalVmName.empty() ? DefaultVmName : threadLocalVmName;
}





// 默认虚拟机名称，可以在 UI 中设置
std::string DefaultVmName = "VM1";

// ==========================================================================================
// ========================== 脚本解析和任务执行功能==========================
// ==========================================================================================

// 执行选定的任务
void ExecuteSelectedTasks(const std::string& vmName) {
    try {

        // 获取选定的任务列表
        const auto& selectedTasks = GetSelectedTasks();
        if (selectedTasks.empty()) {
            AddLogInfo(LogLevel::Warning, "[ScriptExecutor] " + vmName + ": 没有选定的任务");
            return;
        }
        
        // 逐个执行每个选定的脚本
        for (const std::string& taskName : selectedTasks) {
            AddLogInfo(LogLevel::Info, "[ScriptExecutor] " + vmName + ": 开始执行任务 - " + taskName);
            
            // 构建脚本文件路径（这个函数在tasklist.cpp中定义）
            std::string scriptPath = GetScriptFilePath("主线", taskName); // 假设主要在"主线"分类
            
            // 读取脚本文件内容
            std::ifstream file(scriptPath);
            if (!file.is_open()) {
                AddLogInfo(LogLevel::Error, "[ScriptExecutor] " + vmName + ": 无法打开脚本文件: " + scriptPath);
                continue;
            }
            
            std::string scriptContent((std::istreambuf_iterator<char>(file)), std::istreambuf_iterator<char>());
            file.close();
            
            if (scriptContent.empty()) {
                AddLogInfo(LogLevel::Warning, "[ScriptExecutor] " + vmName + ": 脚本文件为空: " + scriptPath);
                continue;
            }
            
            // 加载并运行脚本
            loadAndRunScriptFromString(vmName, scriptContent);
        }
        
    } catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "[ScriptExecutor] " + vmName + ": 执行任务时发生异常: " + std::string(e.what()));
    } catch (...) {
        AddLogInfo(LogLevel::Error, "[ScriptExecutor] " + vmName + ": 执行任务时发生未知异常");
    }
}

// 从字符串加载脚本
void loadAndRunScriptFromString(const std::string& vmName, const std::string& scriptContent) {
    try {

        auto* dispatchCenter = DispatchCenter::getInstance();
        if (!dispatchCenter) {
            AddLogInfo(LogLevel::Error, "[ScriptParser] " + vmName + ": DispatchCenter不可用");
            return;
        }

        VMControllerInfo* controllerInfo = dispatchCenter->getVMControllerInfo(vmName);
        if (!controllerInfo) {
            AddLogInfo(LogLevel::Error, "[ScriptParser] " + vmName + ": 找不到虚拟机控制器");
            return;
        }

        // ✅ 设置脚本解析状态为运行中
        auto* stateManager = VMStateManager::getInstance();
        if (stateManager) {
            stateManager->setScriptRunning(vmName, true);
        }

        // 清空现有任务队列
        for (auto& slicePair : controllerInfo->sliceQueues) {
            if (slicePair.second && slicePair.second->taskQueue) {
                slicePair.second->taskQueue->clear();
            }
        }

        // 解析脚本内容
        parseScriptContent(vmName, scriptContent, controllerInfo);

        // ✅ 脚本解析完成，设置状态为非运行中
        if (stateManager) {
            stateManager->setScriptRunning(vmName, false);
        }

        // 启动轮询器（如果尚未启动）
        dispatchCenter->startTaskPoller(vmName);

        AddLogInfo(LogLevel::Info, "[ScriptParser] " + vmName + ": 脚本解析完成，任务已加载到队列");

    } catch (const std::exception& e) {
        // ✅ 异常时也要清除脚本运行状态
        auto* stateManager = VMStateManager::getInstance();
        if (stateManager) {
            stateManager->setScriptRunning(vmName, false);
        }
        AddLogInfo(LogLevel::Error, "[ScriptParser] " + vmName + ": 脚本解析异常: " + std::string(e.what()));
    } catch (...) {
        // ✅ 异常时也要清除脚本运行状态
        auto* stateManager = VMStateManager::getInstance();
        if (stateManager) {
            stateManager->setScriptRunning(vmName, false);
        }
        AddLogInfo(LogLevel::Error, "[ScriptParser] " + vmName + ": 脚本解析发生未知异常");
    }
}

// 解析脚本内容并生成任务（新添加的核心函数）
void parseScriptContent(const std::string& vmName, const std::string& scriptContent, VMControllerInfo* controllerInfo) {
    if (!controllerInfo) {
        AddLogInfo(LogLevel::Error, "[ScriptParser] " + vmName + ": VMControllerInfo为空");
        return;
    }
    
    // 按行分割脚本内容
    std::vector<std::string> lines;
    std::stringstream ss(scriptContent);
    std::string line;
    while (std::getline(ss, line)) {
        lines.push_back(line);
    }
    
    // 获取可用的切片号
    std::vector<int> availableSlices;
    for (const auto& slicePair : controllerInfo->sliceQueues) {
        availableSlices.push_back(slicePair.first);
    }
    std::sort(availableSlices.begin(), availableSlices.end());
    
    if (availableSlices.empty()) {
        AddLogInfo(LogLevel::Error, "[ScriptParser] " + vmName + ": 没有可用的切片");
        return;
    }
    
    // 解析每个切片的任务 - 统一扫描，正确处理切片分配
    int currentSlice = availableSlices[0]; // 默认使用第一个切片
    auto* dispatchCenter = DispatchCenter::getInstance();

    // 统一扫描：按顺序处理所有行，正确跟踪当前切片
    std::map<std::string, int> labelMap;
    for (size_t i = 0; i < lines.size(); ++i) {
        std::string trimmedLine = trim(lines[i]);

        // 检查切片切换命令
        if (trimmedLine.find("slice(") == 0) {
            size_t sliceEnd = trimmedLine.find(')');
            if (sliceEnd != std::string::npos) {
                std::string sliceStr = trimmedLine.substr(6, sliceEnd - 6);
                try {
                    int newSlice = std::stoi(sliceStr);
                    if (std::find(availableSlices.begin(), availableSlices.end(), newSlice) != availableSlices.end()) {
                        currentSlice = newSlice;
                        AddLogInfo(LogLevel::Info, "[ScriptParser] " + vmName + ": 切换到切片 " + std::to_string(currentSlice));
                    } else {
                        AddLogInfo(LogLevel::Warning, "[ScriptParser] " + vmName + ": 无效的切片号: " + sliceStr);
                    }
                } catch (...) {
                    AddLogInfo(LogLevel::Warning, "[ScriptParser] " + vmName + ": 无效的切片号格式: " + sliceStr);
                }
            }
            continue;
        }

        // 处理LABEL:标签定义命令
        if (trimmedLine.find("LABEL:") == 0) {
            std::string labelName = trimmedLine.substr(6); // 去掉"LABEL:"前缀
            labelName = trim(labelName);
            labelMap[labelName] = static_cast<int>(i);

            // 创建标签定义任务，分配到当前切片
            auto labelTask = std::make_shared<LabelTask>(vmName, labelName, currentSlice);
            dispatchCenter->addTaskToSlice(vmName, currentSlice, labelTask);

            AddLogInfo(LogLevel::Debug, "[ScriptParser] " + vmName + ": 添加标签定义任务: \"" + labelName + "\" 到切片 " + std::to_string(currentSlice));
            continue; // 跳过后续处理，因为这是一个标签行
        }

        // 解析并执行其他命令
        if (!trimmedLine.empty() && trimmedLine[0] != '/' && trimmedLine[0] != '#') {
            parseAndExecuteCommand(vmName, trimmedLine, static_cast<int>(i), currentSlice, scriptContent, labelMap);
        }
    }
}

// 解析并执行命令
void parseAndExecuteCommand(const std::string& vmName, const std::string& line, int lineNumber, 
                           int sliceNumber, const std::string& scriptContent, 
                           const std::map<std::string, int>& labelMap) {
    auto* dispatchCenter = DispatchCenter::getInstance();
    if (!dispatchCenter) {
        AddLogInfo(LogLevel::Error, "[ScriptParser] " + vmName + ": DispatchCenter不可用");
        return;
    }

    try {
        // 检查是否是LABEL:命令（没有括号的命令）
        if (line.find("LABEL:") != std::string::npos) {
            size_t labelPos = line.find("LABEL:");
            std::string labelName = line.substr(labelPos + 6); // 去掉"LABEL:"前缀
            labelName = trim(labelName);

            if (!labelName.empty()) {
                // 创建标签定义任务
                auto labelTask = std::make_shared<LabelTask>(vmName, labelName, sliceNumber);
                dispatchCenter->addTaskToSlice(vmName, sliceNumber, labelTask);

                AddLogInfo(LogLevel::Debug, "[ScriptParser] " + vmName + ": 添加标签定义任务: \"" + labelName + "\" 到切片 " + std::to_string(sliceNumber));
            }
            return;
        }

        // 提取命令和参数
        size_t cmdEnd = line.find('(');
        if (cmdEnd == std::string::npos) {
            return; // 不是有效的命令格式
        }

        std::string command = trim(line.substr(0, cmdEnd));
        
        // 提取参数字符串（去掉括号）
        size_t argsStart = cmdEnd + 1;
        size_t argsEnd = line.find_last_of(')');
        if (argsEnd == std::string::npos || argsEnd <= argsStart) {
            return; // 无效的参数格式
        }

        std::string argsStr = line.substr(argsStart, argsEnd - argsStart);
        std::vector<std::string> args = parseArguments(argsStr);

        // 处理不同类型的命令
        if (command == "LABEL_LOCATE") {
            if (args.empty()) {
                AddLogInfo(LogLevel::Error, "[ScriptParser] " + vmName + ": LABEL_LOCATE命令缺少参数");
                return;
            }

            std::string targetLabel = trim(args[0]);

            // 创建标签定位任务（仅支持文本模式）
            auto labelLocateTask = std::make_shared<LabelLocateTask>(vmName, targetLabel, sliceNumber);
            dispatchCenter->addTaskToSlice(vmName, sliceNumber, labelLocateTask);

            AddLogInfo(LogLevel::Info, "[ScriptParser] " + vmName + ": 添加LABEL_LOCATE任务，目标标签: \"" + targetLabel + "\"");
        }
        else if (command == "TEXT_MATCH") {
            createTextMatchTask(vmName, args, sliceNumber, dispatchCenter);
        }
        else if (command == "VISUAL_MATCH") {
            createVisualMatchTask(vmName, args, sliceNumber, dispatchCenter);
        }
        else if (command == "MOUSE_MOVE") {
            createMouseMoveTask(vmName, args, sliceNumber, dispatchCenter);
        }
        else if (command == "MOUSE_LEFT") {
            createMouseClickTask(vmName, args, sliceNumber, "left", dispatchCenter);
        }
        else if (command == "MOUSE_RIGHT") {
            createMouseClickTask(vmName, args, sliceNumber, "right", dispatchCenter);
        }
        else if (command == "MOUSE_DOUBLE") {
            createMouseClickTask(vmName, args, sliceNumber, "double", dispatchCenter);
        }
        else if (command == "KEYBOARD_INPUT") {
            createKeyboardInputTask(vmName, args, sliceNumber, false, dispatchCenter);
        }
        else if (command == "TITLEBAR_KEYBOARD_INPUT") {
            createKeyboardInputTask(vmName, args, sliceNumber, true, dispatchCenter);
        }
        else if (command == "DELAY") {
            createDelayTask(vmName, args, sliceNumber, dispatchCenter);
        }
        else if (command == "WAIT_FOR_SCREEN_STILL") {
            createWaitForScreenStillTask(vmName, args, sliceNumber, dispatchCenter);
        }
        else if (command == "TEXT_RECOGNIZE") {
            createTextRecognizeTask(vmName, args, sliceNumber, dispatchCenter);
        }
        else if (command == "VISION_MOUSE_DRAG") {
            createVisionMouseDragTask(vmName, args, sliceNumber, dispatchCenter);
        }
        else if (command == "WAIT_FOR_VISUAL_MATCH") {
            createWaitForVisualMatchTask(vmName, args, sliceNumber, dispatchCenter);
        }
        else {
            AddLogInfo(LogLevel::Warning, "[ScriptParser] " + vmName + ": 未知或未实现的命令 '" + command + "' (完整行: '" + line + "')");
        }

    } catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "[ScriptParser] " + vmName + ": 解析命令失败: " + std::string(e.what()));
    }
}

// 工具函数：去除字符串首尾空白
std::string trim(const std::string& str) {
    size_t start = str.find_first_not_of(" \t\n\r");
    if (start == std::string::npos) return "";
    size_t end = str.find_last_not_of(" \t\n\r");
    return str.substr(start, end - start + 1);
}

// 工具函数：解析命令参数
std::vector<std::string> parseArguments(const std::string& argsStr) {
    std::vector<std::string> args;
    if (argsStr.empty()) return args;
    
    std::stringstream ss(argsStr);
    std::string arg;
    while (std::getline(ss, arg, ',')) {
        args.push_back(trim(arg));
    }
    return args;
}

// ==========================================================================================
// ======================= 任务创建函数（将命令转换为任务对象） ========================
// ==========================================================================================

// 创建文字匹配任务
void createTextMatchTask(const std::string& vmName, const std::vector<std::string>& args, 
                        int sliceNumber, DispatchCenter* dispatchCenter) {
    if (args.size() < 2) {
        AddLogInfo(LogLevel::Error, "[ScriptParser] TEXT_MATCH命令参数不足");
        return;
    }
    
    try {
        std::string text = args[0];
        std::string clickType = args[1];
        
        // 默认ROI和阈值
        cv::Rect roi(0, 0, 0, 0);  // 全屏
        double threshold = 0.5;
        
        // 解析可选的ROI参数
        if (args.size() >= 6) {
            int x = std::stoi(args[2]);
            int y = std::stoi(args[3]);
            int width = std::stoi(args[4]);
            int height = std::stoi(args[5]);
            roi = cv::Rect(x, y, width, height);
        }
        
        // 解析可选的阈值参数
        if (args.size() >= 7) {
            threshold = std::stod(args[6]);
        }
        
        // 使用DispatchCenter的API创建文字匹配任务
        auto future = dispatchCenter->addWordsMatchTask(
            vmName, 
            std::vector<std::string>{text}, 
            threshold, 
            roi, 
            sliceNumber, 
            clickType  // 直接使用字符串类型
        );
        
    } catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "[ScriptParser] 创建TEXT_MATCH任务失败: " + std::string(e.what()));
    }
}

// 创建图像匹配任务
void createVisualMatchTask(const std::string& vmName, const std::vector<std::string>& args, 
                          int sliceNumber, DispatchCenter* dispatchCenter) {
    if (args.size() < 2) {
        AddLogInfo(LogLevel::Error, "[ScriptParser] VISUAL_MATCH命令参数不足");
        return;
    }
    
    try {
        std::string templateName = args[0];
        std::string clickType = args[1];
        
        // 默认ROI和阈值
        cv::Rect roi(0, 0, 0, 0);  // 全屏
        double threshold = 0.8;
        
        // 解析可选的ROI参数
        if (args.size() >= 6) {
            int x = std::stoi(args[2]);
            int y = std::stoi(args[3]);
            int width = std::stoi(args[4]);
            int height = std::stoi(args[5]);
            roi = cv::Rect(x, y, width, height);
        }
        
        // 解析可选的阈值参数
        if (args.size() >= 7) {
            threshold = std::stod(args[6]);
        }
        
        // 使用DispatchCenter的API创建图像匹配任务
        auto future = dispatchCenter->addImageMatchTask(
            vmName, 
            templateName, 
            threshold, 
            "", // imagePath
            roi, 
            sliceNumber, 
            clickType  // 直接使用字符串类型
        );
        
    } catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "[ScriptParser] 创建VISUAL_MATCH任务失败: " + std::string(e.what()));
    }
}

// 创建鼠标移动任务
void createMouseMoveTask(const std::string& vmName, const std::vector<std::string>& args, 
                        int sliceNumber, DispatchCenter* dispatchCenter) {
    if (args.size() < 2) {
        AddLogInfo(LogLevel::Error, "[ScriptParser] MOUSE_MOVE命令参数不足");
        return;
    }
    
    try {
        int x = std::stoi(args[0]);
        int y = std::stoi(args[1]);
        
        // 使用DispatchCenter的API创建鼠标移动任务
        std::vector<InputTask> actions;
        InputTask moveAction;
        moveAction.type = InputType::MOUSE_MOVE;
        moveAction.x = x;
        moveAction.y = y;
        actions.push_back(moveAction);
        
        auto future = dispatchCenter->addDirectKeyboardMouseActionTask(vmName, actions, sliceNumber);
        
    } catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "[ScriptParser] 创建MOUSE_MOVE任务失败: " + std::string(e.what()));
    }
}

// 创建鼠标点击任务
void createMouseClickTask(const std::string& vmName, const std::vector<std::string>& args, 
                         int sliceNumber, const std::string& clickType, DispatchCenter* dispatchCenter) {
    try {
        InputType inputType = InputType::MOUSE_LEFT_CLICK;
        
        if (clickType == "right") {
            inputType = InputType::MOUSE_RIGHT_CLICK;
        } else if (clickType == "double") {
            inputType = InputType::MOUSE_LEFT_CLICK; // 双击会需要特殊处理
        }
        
        std::vector<InputTask> actions;
        
        // 如果有坐标参数，先移动再点击
        if (args.size() >= 2) {
            int x = std::stoi(args[0]);
            int y = std::stoi(args[1]);
            
            // 先创建移动任务
            InputTask moveAction;
            moveAction.type = InputType::MOUSE_MOVE;
            moveAction.x = x;
            moveAction.y = y;
            actions.push_back(moveAction);
            
            // 再创建点击任务
            InputTask clickAction;
            clickAction.type = inputType;
            clickAction.x = x;
            clickAction.y = y;
            actions.push_back(clickAction);
            
            // 双击需要两次点击
            if (clickType == "double") {
                actions.push_back(clickAction);
            }
            
        } else {
            // 在当前位置点击
            InputTask clickAction;
            clickAction.type = inputType;
            clickAction.x = 0;
            clickAction.y = 0;
            actions.push_back(clickAction);
            
            // 双击需要两次点击
            if (clickType == "double") {
                actions.push_back(clickAction);
            }
        }
        
        auto future = dispatchCenter->addDirectKeyboardMouseActionTask(vmName, actions, sliceNumber);
        
    } catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "[ScriptParser] 创建MOUSE_" + clickType + "任务失败: " + std::string(e.what()));
    }
}

// 创建键盘输入任务
void createKeyboardInputTask(const std::string& vmName, const std::vector<std::string>& args, 
                           int sliceNumber, bool isTitleBar, DispatchCenter* dispatchCenter) {
    if (args.size() < 3) {
        AddLogInfo(LogLevel::Error, "[ScriptParser] KEYBOARD_INPUT命令参数不足，至少需要3个参数(最小延迟,最大延迟,按键...)");
        return;
    }
    
    try {
        // 初始化随机种子（基于当前时间）
        static bool seedInitialized = false;
        if (!seedInitialized) {
            srand(static_cast<unsigned int>(time(nullptr)));
            seedInitialized = true;
        }
        
        // 解析延迟范围参数
        int minDelayMs = std::stoi(args[0]);
        int maxDelayMs = std::stoi(args[1]);
        
        // 验证延迟范围
        if (minDelayMs < 0 || maxDelayMs < 0 || minDelayMs > maxDelayMs) {
            AddLogInfo(LogLevel::Error, "[ScriptParser] KEYBOARD_INPUT延迟参数无效: " + args[0] + "," + args[1]);
            return;
        }
        
        // 创建输入任务序列
        std::vector<InputTask> actions;
        
        // 如果是标题栏键盘输入，先点击标题栏激活
        if (isTitleBar) {
            // 标题栏区域：100,5,600,25
            int titleBarX = 100 + rand() % (600 - 100);  // 100-600范围内随机X
            int titleBarY = 5 + rand() % (25 - 5);       // 5-25范围内随机Y
            
            // 添加移动到标题栏的动作
            InputTask moveAction;
            moveAction.type = InputType::MOUSE_MOVE;
            moveAction.x = titleBarX;
            moveAction.y = titleBarY;
            actions.push_back(moveAction);
            
            // 添加左键点击动作
            InputTask clickAction;
            clickAction.type = InputType::MOUSE_LEFT_CLICK;
            actions.push_back(clickAction);
            
        }
        
        // 添加键盘输入任务，从第3个参数开始（索引2）
        for (size_t i = 2; i < args.size(); ++i) {
            const auto& key = args[i];
            
            // 如果不是第一个键，先添加随机延迟
            if (i > 2) {
                int randomDelay = minDelayMs + rand() % (maxDelayMs - minDelayMs + 1);
                InputTask delayAction;
                delayAction.type = InputType::DELAY;
                delayAction.delayMs = randomDelay;
                actions.push_back(delayAction);
                
                AddLogInfo(LogLevel::Debug, "[ScriptParser] " + vmName + ": 键盘输入延迟 " + std::to_string(randomDelay) + "ms");
            }
            
            // 使用createKeyboardTask函数正确创建键盘任务
            InputTask keyAction = createKeyboardTask(key);
            actions.push_back(keyAction);
        }
        
        
        auto future = dispatchCenter->addDirectKeyboardMouseActionTask(vmName, actions, sliceNumber);
        
    } catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "[ScriptParser] 创建KEYBOARD_INPUT任务失败: " + std::string(e.what()));
    }
}

// 创建延时任务
void createDelayTask(const std::string& vmName, const std::vector<std::string>& args,
                    int sliceNumber, DispatchCenter* dispatchCenter) {
    if (args.empty()) {
        AddLogInfo(LogLevel::Error, "[ScriptParser] DELAY命令参数不足");
        return;
    }

    try {
        int delayMs = std::stoi(args[0]);

        // ✅ 修复：创建延时任务而不是直接执行
        // 使用特殊的InputTask来表示延时操作
        std::vector<InputTask> actions;
        InputTask delayAction;
        delayAction.type = InputType::DELAY;  // 需要在InputType枚举中添加DELAY
        delayAction.delayMs = delayMs;        // 需要在InputTask结构中添加delayMs字段
        actions.push_back(delayAction);

        auto future = dispatchCenter->addDirectKeyboardMouseActionTask(vmName, actions, sliceNumber);

    } catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "[ScriptParser] 创建DELAY任务失败: " + std::string(e.what()));
    }
}

// 创建标签定位任务（仅支持文本模式）
void createLabelLocateTask(const std::string& vmName, const std::vector<std::string>& args,
                          int sliceNumber, const std::string& scriptContent, DispatchCenter* dispatchCenter) {
    if (args.empty()) {
        AddLogInfo(LogLevel::Error, "[ScriptParser] LABEL_LOCATE命令参数不足");
        return;
    }

    try {
        // 文本模式：LABEL_LOCATE(targetText)
        std::string targetText = args[0];

        AddLogInfo(LogLevel::Info, "[ScriptParser] " + vmName + ": 创建LABEL_LOCATE任务 - 目标标签: \"" + targetText + "\"");

        // 创建标签定位任务
        auto labelLocateTask = std::make_shared<LabelLocateTask>(vmName, targetText, sliceNumber);
        dispatchCenter->addTaskToSlice(vmName, sliceNumber, labelLocateTask);

    } catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "[ScriptParser] 创建LABEL_LOCATE任务失败: " + std::string(e.what()));
    }
}

// 创建等待画面静止任务
void createWaitForScreenStillTask(const std::string& vmName, const std::vector<std::string>& args, 
                                 int sliceNumber, DispatchCenter* dispatchCenter) {
    if (args.size() < 6) {
        AddLogInfo(LogLevel::Error, "[ScriptParser] WAIT_FOR_SCREEN_STILL命令参数不足");
        return;
    }
    
    try {
        int x = std::stoi(args[0]);
        int y = std::stoi(args[1]);
        int width = std::stoi(args[2]);
        int height = std::stoi(args[3]);
        int checkInterval = std::stoi(args[4]);
        int maxTimeout = std::stoi(args[5]);
        
        cv::Rect roi(x, y, width, height);
        
        // 使用DispatchCenter的API创建等待画面静止任务
        auto future = dispatchCenter->addWaitForScreenStillTask(
            vmName, 
            sliceNumber, 
            x, y, width, height, 
            checkInterval, 
            maxTimeout
        );
        
    } catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "[ScriptParser] 创建WAIT_FOR_SCREEN_STILL任务失败: " + std::string(e.what()));
    }
}

// 创建文本识别任务
void createTextRecognizeTask(const std::string& vmName, const std::vector<std::string>& args, 
                           int sliceNumber, DispatchCenter* dispatchCenter) {
    if (args.size() < 4) {
        AddLogInfo(LogLevel::Error, "[ScriptParser] TEXT_RECOGNIZE命令参数不足");
        return;
    }
    
    try {
        int x = std::stoi(args[0]);
        int y = std::stoi(args[1]);
        int width = std::stoi(args[2]);
        int height = std::stoi(args[3]);
        
        cv::Rect roi(x, y, width, height);
        
        // 使用DispatchCenter的API创建文本识别任务
        auto future = dispatchCenter->addTextRecognizeTask(vmName, roi, sliceNumber);
        
    } catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "[ScriptParser] 创建TEXT_RECOGNIZE任务失败: " + std::string(e.what()));
    }
}

// 创建视觉鼠标拖动任务
void createVisionMouseDragTask(const std::string& vmName, const std::vector<std::string>& args, 
                              int sliceNumber, DispatchCenter* dispatchCenter) {
    if (args.size() < 8) {
        AddLogInfo(LogLevel::Error, "[ScriptParser] VISION_MOUSE_DRAG命令参数不足");
        return;
    }
    
    try {
        std::string templateName = args[0];
        int roiX = std::stoi(args[1]);
        int roiY = std::stoi(args[2]);
        int roiWidth = std::stoi(args[3]);
        int roiHeight = std::stoi(args[4]);
        int targetX = std::stoi(args[5]);
        int targetY = std::stoi(args[6]);
        double threshold = std::stod(args[7]);
        
        cv::Rect roi(roiX, roiY, roiWidth, roiHeight);
        
        // 先进行图像匹配找到目标位置
        auto matchFuture = dispatchCenter->addImageMatchTask(
            vmName, 
            templateName, 
            threshold, 
            "", // imagePath
            roi, 
            sliceNumber, 
            "none" // 不点击，只匹配
        );
        
        // 等待匹配结果
        auto matchResult = matchFuture.get();
        
        if (matchResult.success) {
            // 匹配成功，执行拖动操作
            std::vector<InputTask> actions;
            
            // 移动到匹配位置
            InputTask moveAction;
            moveAction.type = InputType::MOUSE_MOVE;
            moveAction.x = matchResult.matchLocation.x;
            moveAction.y = matchResult.matchLocation.y;
            actions.push_back(moveAction);
            
            // 按下鼠标左键
            InputTask downAction;
            downAction.type = InputType::MOUSE_LEFT_CLICK;
            actions.push_back(downAction);
            
            // 移动到目标位置
            InputTask dragAction;
            dragAction.type = InputType::DRAGMOUSE;
            dragAction.x = targetX;
            dragAction.y = targetY;
            actions.push_back(dragAction);
            
            // 释放鼠标左键
            InputTask upAction;
            upAction.type = InputType::MOUSE_LEFT_CLICK;
            actions.push_back(upAction);
            
            auto dragFuture = dispatchCenter->addDirectKeyboardMouseActionTask(vmName, actions, sliceNumber);
            
        } else {
            AddLogInfo(LogLevel::Error, "[ScriptParser] " + vmName + ": VISION_MOUSE_DRAG匹配失败，无法执行拖动");
        }
        
    } catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "[ScriptParser] 创建VISION_MOUSE_DRAG任务失败: " + std::string(e.what()));
    }
}

// 创建等待视觉匹配任务
void createWaitForVisualMatchTask(const std::string& vmName, const std::vector<std::string>& args, 
                                 int sliceNumber, DispatchCenter* dispatchCenter) {
    if (args.size() < 4) {
        AddLogInfo(LogLevel::Error, "[ScriptParser] WAIT_FOR_VISUAL_MATCH命令参数不足");
        return;
    }
    
    try {
        std::string templateName = args[0];
        int checkIntervalSeconds = std::stoi(args[1]);
        int maxTimeoutSeconds = std::stoi(args[2]);
        double threshold = std::stod(args[3]);
        
        // 使用DispatchCenter的API创建等待视觉匹配任务
        auto future = dispatchCenter->addWaitForVisualMatchTask(
            vmName, 
            sliceNumber, 
            templateName, 
            checkIntervalSeconds, 
            maxTimeoutSeconds, 
            threshold
        );
        
    } catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "[ScriptParser] 创建WAIT_FOR_VISUAL_MATCH任务失败: " + std::string(e.what()));
    }
}






#pragma once
#include <queue>
#include <deque>
#include <mutex>
#include <condition_variable>
#include <optional> // For std::optional
#include <chrono>   // For std::chrono::milliseconds
#include <utility>  // For std::move
#include <stdexcept>//

// 模板类 ThreadSafeQueue 的定义和实现都在头文件中
template<typename T>
class ThreadSafeQueue {
private:
    std::deque<T> queue_;  // 改用 deque 作为底层容器，支持双端操作
    mutable std::mutex mutex_; // mutable 允许 const 方法（如 empty()）锁定互斥量
    std::condition_variable cond_var_not_empty_; // 用于等待弹出的线程（队列不为空）
    std::condition_variable cond_var_not_full_;  // 用于等待推送的线程（队列未满，受容量限制）
    const size_t capacity_; // 最大允许的元素数量

public:
    // 构造函数需要一个正数容量
    explicit ThreadSafeQueue(size_t capacity) : capacity_(capacity) {
        if (capacity_ == 0) {
            // 容量为 0 对于队列来说通常不是一个有用的场景
            throw std::runtime_error("ThreadSafeQueue capacity cannot be zero.");
        }
    }

    // 禁用拷贝和赋值，以防止资源管理问题
    ThreadSafeQueue(const ThreadSafeQueue&) = delete;
    ThreadSafeQueue& operator=(const ThreadSafeQueue&) = delete;

    // 推送一个项目到队列尾部。如果队列满了，则阻塞。
    void push(T item) {
        std::unique_lock<std::mutex> lock(mutex_);  // 条件等待,使用 unique_lock

        // 等待直到队列有空间 (大小 < 容量)
        cond_var_not_full_.wait(lock, [this] {
            return queue_.size() < capacity_;
            });

        // 现在，锁已持有且有空间可用，推送项目到尾部
        queue_.push_back(std::move(item));  // 使用移动语义提高效率

        // 通知一个等待弹出的线程（因为推送后队列保证不再为空）
        cond_var_not_empty_.notify_one();
    }

    // 推送一个项目到队列头部（用于重试失败的任务）。如果队列满了，则阻塞。
    void push_front(T item) {
        std::unique_lock<std::mutex> lock(mutex_);  // 条件等待,使用 unique_lock

        // 等待直到队列有空间 (大小 < 容量)
        cond_var_not_full_.wait(lock, [this] {
            return queue_.size() < capacity_;
            });

        // 现在，锁已持有且有空间可用，将项目直接插入到队列头部
        queue_.push_front(std::move(item));  // 直接使用 deque 的 push_front，O(1) 时间复杂度

        // 通知一个等待弹出的线程（因为推送后队列保证不再为空）
        cond_var_not_empty_.notify_one();
    }

    // 阻塞式弹出 - 无限期等待直到有项目可用，然后移除并返回它。
    T pop() {
        std::unique_lock<std::mutex> lock(mutex_); // 条件等待使用 unique_lock

        // 等待直到队列不为空
        cond_var_not_empty_.wait(lock, [this] {
            return !queue_.empty();
            });

        // 现在，锁已持有且队列不为空，获取并移除项目
        T item = std::move(queue_.front());
        queue_.pop_front(); // 使用 deque 的 pop_front

        // 通知一个等待推送的线程（因为弹出后现在空间可用了）
        cond_var_not_full_.notify_one();

        return item;
    }

    // 非阻塞式 try_pop - 尝试弹出一个项目。
    // 如果队列为空，返回 std::nullopt，否则返回封装在 std::optional 中的项目
    std::optional<T> try_pop() {
        std::lock_guard<std::mutex> lock(mutex_);

        if (queue_.empty()) {
            return std::nullopt; // 队列为空，没有可弹出的
        }

        // 队列不为空，获取并移除项目
        T item = std::move(queue_.front());
        queue_.pop_front();// 使用 deque 的 pop_front

        // 通知一个等待推送的线程
        cond_var_not_full_.notify_one();

        return item;
    }

    // 带超时的 pop 版本 - 等待指定时长以获取项目。
       // 如果在超时时间内成功弹出了项目，返回 true，否则返回 false。
       // 如果成功，弹出的项目会通过 'item' 引用参数移出。
    bool pop(T& item, std::chrono::milliseconds timeout) {
        std::unique_lock<std::mutex> lock(mutex_); 

        // 等待直到队列不为空 或 超时时间到期
        // wait_for 返回 true 如果谓词返回 true，超时则返回 false
        bool success = cond_var_not_empty_.wait_for(lock, timeout, [this] {
            return !queue_.empty();
            });

        if (!success) {
            // 谓词 (!queue_.empty()) 为 false 且超时发生。
            // 没有可用的项目。
            return false;
        }

        // 在超时时间内谓词返回 true（队列不为空），并且锁已持有。继续弹出。
        item = std::move(queue_.front());// 将项目移动到输出参数中
        queue_.pop_front();// 使用 deque 的 pop_front

        // 通知一个等待推送的线程（空间现在可用）
        cond_var_not_full_.notify_one();

        return true;
    }

    // 检查队列是否为空。注意：这是一个快照，调用后可能立即改变。
    bool empty() const {
        std::lock_guard<std::mutex> lock(mutex_);// 锁定以保证线程安全
        return queue_.empty();
    }

    // 获取队列当前大小。注意：这是一个快照，调用后可能立即改变。
    size_t size() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return queue_.size();
    }

    // 获取队列的最大容量
    size_t capacity() const {
        return capacity_;  // capacity_ 是 const，读取其值无需锁定
    }

    // 非阻塞式尝试弹出任务（by reference） 的版本
    //bool try_pop(T& item) {
    //    std::lock_guard<std::mutex> lock(mutex_);
    //    if (queue_.empty()) {
    //        return false;
    //    }
    //    item = std::move(queue_.front());
    //    queue_.pop_front();
    //    cond_var_not_full_.notify_one(); // 弹出后可能空出位置，唤醒 push 等待者
    //    return true;
    //}
	//清理队列
    void clear() {
        std::lock_guard<std::mutex> lock(mutex_);
        queue_.clear();  // deque 支持直接清空
    }

    // 查看指定索引位置的元素，但不移除它
    std::optional<T> peek_at(size_t index) const {
        std::lock_guard<std::mutex> lock(mutex_);
        if (index >= queue_.size()) {
            return std::nullopt;
        }
        return queue_[index];
    }
};
#pragma once

#include <opencv2/opencv.hpp>
#include <vector>
#include <string>
#include <memory>
#include <map>
#include <atomic>
#include "VNCControl.h"
#include "threadsafequeue.h"

// 前向声明
class IVMTask;
class VMStateManager;

// **面向对象的动态容器管理：切片容器类**
class SliceContainer {
private:
    size_t currentTaskIndex;                              // **核心：当前正在执行或下一个要执行的任务索引**
    std::string id;                                       // 切片ID（如"VM1-slice-1"）
    std::unique_ptr<ThreadSafeQueue<std::shared_ptr<IVMTask>>> taskQueue; // 现有的线程安全队列
    std::map<std::string, size_t> labelPositions;        // 标签位置映射
    
public:
    // 构造函数
    SliceContainer(const std::string& sliceId);
    
    // 禁用拷贝，允许移动
    SliceContainer(const SliceContainer&) = delete;
    SliceContainer& operator=(const SliceContainer&) = delete;
    SliceContainer(SliceContainer&&) = default;
    SliceContainer& operator=(SliceContainer&&) = default;
    
    // **核心方法：基于索引的任务访问**
    std::shared_ptr<IVMTask> peekCurrentTask();          // 查看当前任务但不移除
    void moveNext();                                     // 移动到下一个任务
    bool jumpToLabel(const std::string& labelName);     // 跳转到指定标签
    bool jumpToIndex(size_t index);                      // 跳转到指定索引
    bool hasMoreTasks() const;                          // 检查是否还有任务
    size_t getCurrentIndex() const;                     // 获取当前索引
    size_t getTaskCount() const;                        // 获取任务总数
    void resetToBeginning();                            // 重置到开始位置
    
    // **任务管理方法**
    void addTask(std::shared_ptr<IVMTask> task);        // 添加任务
    void clearAllTasks();                               // 清空所有任务
    
    // **状态检查方法**
    bool canExecuteTask(const std::string& vmName, int sliceNumber) const;  // 检查切片是否可以执行任务
    
    // **访问器**
    const std::string& getId() const { return id; }
    
private:
    // **辅助方法：从任务中提取标签名**
    std::string extractLabelName(std::shared_ptr<IVMTask> task) const;
};

class ScreenFrameDistributor {
public:
    // 构造函数
    ScreenFrameDistributor(int rows, int cols);
    
    // 设置切片行数和列数
    void setGridSize(int rows, int cols);
    

    
    // 获取总切片数量
    int getTotalSlices() const;
    
    // 获取指定编号的切片图像
    cv::Mat getSliceImage(const cv::Mat& frame, int sliceNumber) const;
    

    
    // 获取切片的行数和列数
    int getRows() const { return rows_; }
    int getCols() const { return cols_; }

    cv::Point toOriginal(const cv::Point& localPoint, int sliceNumber, const cv::Mat& sliceImage) const;

    // 坐标转换相关方法
    cv::Point sliceToGlobalCoordinate(const cv::Point& slicePoint, int sliceNumber) const;
    cv::Rect sliceToGlobalROI(const cv::Rect& sliceROI, int sliceNumber) const;
    cv::Point globalToSliceCoordinate(const cv::Point& globalPoint, int sliceNumber) const;
    
    // 输入任务坐标转换
    std::vector<InputTask> convertSliceInputTasksToGlobal(int sliceNumber, const std::vector<InputTask>& sliceTasks) const;
    
    // 新增：获取系统屏幕尺寸
    static std::pair<int, int> getScreenSize();

    // 切片配置管理工具函数
    static bool isValidSliceConfiguration(int rows, int cols);

    static std::string getSliceConfigurationDescription(int rows, int cols);
    
    // 切片编号验证
    bool isValidSliceNumber(int sliceNumber) const;
    
    // 获取切片配置的详细信息
    std::string getConfigurationInfo() const;
    
    // 生成切片顺序
    static std::vector<int> generateSliceOrder(int rows, int cols);

    // 🔧 新增：设置和获取VNC分辨率（虚拟机内部分辨率）
    void setVNCResolution(int width, int height);
    std::pair<int, int> getVNCResolution() const;
    bool hasVNCResolution() const;
    
    // **🎯 切片容器管理方法**
    void initializeSliceContainers(const std::string& vmName);  // 初始化切片容器
    SliceContainer* getSliceContainer(int sliceNumber);         // 获取指定切片容器
    const SliceContainer* getSliceContainer(int sliceNumber) const; // 获取指定切片容器（const版本）
    void clearAllSliceContainers();                            // 清空所有切片容器
    bool hasSliceContainer(int sliceNumber) const;              // 检查是否有指定切片容器
    
    // **任务管理代理方法（供DispatchCenter调用）**
    bool addTaskToSlice(int sliceNumber, std::shared_ptr<IVMTask> task);           // 添加任务到指定切片
    std::shared_ptr<IVMTask> peekTaskFromSlice(int sliceNumber);                  // 查看指定切片的当前任务
    void moveToNextTaskInSlice(int sliceNumber);                                  // 移动到下一个任务
    bool jumpToLabelInSlice(int sliceNumber, const std::string& labelName);       // 在指定切片中跳转到标签
    void clearTasksInSlice(int sliceNumber);                                      // 清空指定切片的任务
    bool hasMoreTasksInSlice(int sliceNumber) const;                             // 检查指定切片是否还有任务
    size_t getTaskCountInSlice(int sliceNumber) const;                           // 获取指定切片的任务数量

private:
    int rows_;
    int cols_;
    
    // 🔧 VNC分辨率（虚拟机内部分辨率）
    mutable int vncWidth_ = 0;
    mutable int vncHeight_ = 0;
    mutable bool hasVNCResolution_ = false;
    
    // **🎯 切片容器映射**
    std::map<int, std::unique_ptr<SliceContainer>> sliceContainers_;  // 切片编号 -> 切片容器
    std::string vmName_;                                              // 关联的虚拟机名称
    
    // 计算指定编号的切片在原图中的位置
    cv::Rect calculateSliceRect(int sliceNumber) const;
};

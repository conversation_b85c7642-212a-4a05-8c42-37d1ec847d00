#pragma once

#include <opencv2/opencv.hpp>
#include <vector>
#include <string>
#include <memory>
#include <map>
#include <atomic>
#include "VNCControl.h"
#include "threadsafequeue.h"

// 前向声明
class IVMTask;
class VMStateManager;



class ScreenFrameDistributor {
public:
    // 构造函数
    ScreenFrameDistributor(int rows, int cols);
    
    // 设置切片行数和列数
    void setGridSize(int rows, int cols);
    

    
    // 获取总切片数量
    int getTotalSlices() const;
    
    // 获取指定编号的切片图像
    cv::Mat getSliceImage(const cv::Mat& frame, int sliceNumber) const;
    

    
    // 获取切片的行数和列数
    int getRows() const { return rows_; }
    int getCols() const { return cols_; }

    cv::Point toOriginal(const cv::Point& localPoint, int sliceNumber, const cv::Mat& sliceImage) const;

    // 坐标转换相关方法
    cv::Point sliceToGlobalCoordinate(const cv::Point& slicePoint, int sliceNumber) const;
    cv::Rect sliceToGlobalROI(const cv::Rect& sliceROI, int sliceNumber) const;
    cv::Point globalToSliceCoordinate(const cv::Point& globalPoint, int sliceNumber) const;
    
    // 输入任务坐标转换
    std::vector<InputTask> convertSliceInputTasksToGlobal(int sliceNumber, const std::vector<InputTask>& sliceTasks) const;
    
    // 新增：获取系统屏幕尺寸
    static std::pair<int, int> getScreenSize();

    // 切片配置管理工具函数
    static bool isValidSliceConfiguration(int rows, int cols);

    static std::string getSliceConfigurationDescription(int rows, int cols);
    
    // 切片编号验证
    bool isValidSliceNumber(int sliceNumber) const;
    
    // 获取切片配置的详细信息
    std::string getConfigurationInfo() const;
    
    // 生成切片顺序
    static std::vector<int> generateSliceOrder(int rows, int cols);

    // 🔧 新增：设置和获取VNC分辨率（虚拟机内部分辨率）
    void setVNCResolution(int width, int height);
    std::pair<int, int> getVNCResolution() const;
    bool hasVNCResolution() const;
    


private:
    int rows_;
    int cols_;
    
    // 🔧 VNC分辨率（虚拟机内部分辨率）
    mutable int vncWidth_ = 0;
    mutable int vncHeight_ = 0;
    mutable bool hasVNCResolution_ = false;
    

    
    // 计算指定编号的切片在原图中的位置
    cv::Rect calculateSliceRect(int sliceNumber) const;
};

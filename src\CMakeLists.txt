# 针对 MSVC 编译器的设置
if(MSVC)
    add_compile_options("/utf-8")
    add_compile_definitions(
        _UNICODE
        UNICODE
        NOMINMAX
        WIN32_LEAN_AND_MEAN
    )
endif()

#PddleOcr设置PaddleOCR库路径
set(PADDLEOCR_INCLUDE_DIR ${CMAKE_SOURCE_DIR}/extern/paddleocr_v1/include)
set(PADDLEOCR_LIB_DIR ${CMAKE_SOURCE_DIR}/lib)
set(PADDLEOCR_MODELS ${CMAKE_SOURCE_DIR}/extern/paddleocr_v1/MODELS)

# PddleOcr设置OpenCV路径
set(OpenCV_DIR "D:/opencv/build")
set(OpenCV_INCLUDE_DIRS "${OpenCV_DIR}/include")
set(OpenCV_LIBS_DIR "${OpenCV_DIR}/x64/vc16/lib")
set(OpenCV_BIN_DIR "${OpenCV_DIR}/x64/vc16/bin")

# PddleOcr设置OpenCV库
set(OpenCV_LIBS "${OpenCV_LIBS_DIR}/opencv_world4100.lib")

# 添加Windows特定的定义
#add_definitions(-DNOMINMAX -D_USE_MATH_DEFINES -D_CRT_SECURE_NO_WARNINGS)

# 移除Qt依赖，使用Windows原生WinHTTP

# 收集源文件
file(GLOB_RECURSE SOURCE_FILES 
    "main.cpp"
    "GUI/*.cpp"
    "GUI/consolelog/*.cpp"
    "GUI/systeminfo/*.cpp"
    "GUI/hardwareinfo/*.cpp"
    "GUI/centerpanel/*.cpp"
    "GUI/tasklist/*.cpp"
    "GUI/scripteditor/*.cpp"
    "GUI/visionconfig/*.cpp"
    #"InputSimulator/sendInput/*.cpp"
    #"InputSimulator/sendMessage/*.cpp"
    "Core/fwindows/*.cpp"
    "Core/thread_queue/*.cpp"
    "Core/TaskManager.cpp"
    "Core/VersionChecker.cpp"
    "Core/ThreadPool.cpp"
    "Core/UnifiedThreadManager.cpp"
    "Core/ConfigManager.cpp"
    "Vnc/VncMain.cpp"
    "Vnc/DispatchCenter.cpp"
    "Vnc/VMStateManager.cpp"
    "Vnc/VNCControl.cpp"
    "Vnc/MouseBoardCode.cpp"
    "Vnc/VMVision.cpp"
    "Vnc/VMTasks.cpp"
    "Vnc/ScreenFrameDistributor.cpp"
    "Logic/common.cpp"
    "config/TemplatePathManager.cpp"
    "config/ConfigLoader.cpp"
    "GUI/LoadingManager.cpp"
)

# 收集头文件
file(GLOB_RECURSE HEADER_FILES
    "GUI/*.h"
    "GUI/consolelog/*.h"
    "GUI/systeminfo/*.h"
    "GUI/hardwareinfo/*.h"
    "GUI/centerpanel/*.h"
    "GUI/tasklist/*.h"
    "GUI/scripteditor/*.h"
    "GUI/visionconfig/*.h"
    #"InputSimulator/sendInput/*.h"
    #"InputSimulator/sendMessage/*.h"
    "Core/fwindows/*.h"
    "Core/thread_queue/*.h"
    "Core/VersionChecker.h"
    "Core/UnifiedThreadManager.h"
    "Core/ThreadPool.h"
    "Core/ConfigManager.h"
    "Core/Exceptions.h"
    "Vnc/VncMain.h"
    "Vnc/DispatchCenter.h"
    "Vnc/VMStateManager.h"
    "Vnc/threadsafequeue.h"
    "Vnc/VNCControl.h"
    "Vnc/MouseBoardCode.h"
    "Vnc/VMVision.h"
    "Vnc/VMTasks.h"
    "Vnc/ScreenFrameDistributor.h"
    "Logic/common.h"
    "config/TemplatePathManager.h"
    "config/ConfigLoader.h"
    "config/LRUCache.h"
    "GUI/LoadingManager.h"
)


# 生成可执行程序，使用顶层项目名称 qnyh_xy_auto1
add_executable(${PROJECT_NAME} ${SOURCE_FILES} ${HEADER_FILES})

target_link_directories(${PROJECT_NAME} PRIVATE
    D:/libvncserver/build/Release
    D:/vcpkg/installed/x64-windows/lib
    #D:/opencv/build/x64/vc16/lib # OpenCV 的链接目录由 find_package 处理
    ${PADDLEOCR_LIB_DIR}
    ${OpenCV_LIBS_DIR}
)

# 添加 ImGui 静态库
set(CJSON_DIR "${CMAKE_SOURCE_DIR}/extern/cjson")
target_include_directories(${PROJECT_NAME} PRIVATE 
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CJSON_DIR}
    D:/libvncserver/include
    D:/libvncserver/include/rfb
    D:/libvncserver/build/include
    D:/vcpkg/installed/x64-windows/include
    ${PADDLEOCR_INCLUDE_DIR}
    ${OpenCV_INCLUDE_DIRS}
)

# 链接 ImGui 和 DirectX 11 所需的库
target_link_libraries(${PROJECT_NAME} PRIVATE 
    imgui 
    d3d11 
    dxgi
    vncclient
    ws2_32
    zlib
    winhttp #使用Windows原生WinHTTP
        #D:/opencv/build/x64/vc16/lib/opencv_world4110d.lib
    ${OpenCV_LIBS} # 这个变量包含了需要链接的正确库
    PaddleOCR
)
